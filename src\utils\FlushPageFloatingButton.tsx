/**
 * Flush Page Floating Button Component
 *
 * A development utility component that provides a floating button for manual
 * page cache invalidation. This tool helps developers and content managers
 * immediately see content changes without waiting for natural cache expiration.
 *
 * Features:
 * - Manual page-specific cache invalidation
 * - Support for localized pages with language parameters
 * - Visual feedback with loading, success, and error states
 * - Automatic page reload after successful cache flush
 * - Only visible in non-production environments
 *
 * Cache Strategy:
 * - Generates multiple slug variants (with/without trailing slashes)
 * - Handles language-specific URLs for internationalization
 * - Uses the page revalidation API for targeted cache clearing
 *
 * UI/UX:
 * - Fixed positioning in bottom-left corner
 * - Circular button with hover effects
 * - Status messages with auto-dismiss functionality
 * - Disabled state during loading to prevent multiple requests
 */

'use client'

import { useEffect, useState } from 'react'

/**
 * FlushPageFloatingButton Component
 * Provides manual cache invalidation functionality for the current page
 */
const FlushPageFloatingButton = () => {
    const [error, setError] = useState<string | null>(null)
    const [loading, setLoading] = useState<boolean>(false)
    const [success, setSuccess] = useState<string | null>(null)

    /**
     * Auto-dismiss status messages after 5 seconds
     * Provides clean UI by automatically clearing success/error states
     */
    useEffect(() => {
        if (success || error) {
            const timer = setTimeout(() => {
                setError(null)
                setSuccess(null)
            }, 5000) // Reset status messages after 5 seconds
            return () => clearTimeout(timer) // Cleanup timeout on unmount
        }
    }, [success, error])

    /**
     * FlushPage Function
     *
     * Handles the cache invalidation process for the current page.
     * Extracts URL information, generates appropriate slug variants,
     * calls the revalidation API, and provides user feedback.
     */
    const FlushPage = async () => {
        // Reset UI state for new flush operation
        setLoading(true)
        setError(null)
        setSuccess(null)

        try {
            // Ensure we're running in browser environment
            if (typeof window === 'undefined') {
                throw new Error('Window is not available')
            }

            // Parse current page URL for revalidation parameters
            const url = window.location.href
            const parsedUrl = new URL(url)

            // Extract base Vercel deployment URL
            const vercelUrl = `${parsedUrl.protocol}//${parsedUrl.host}`

            // Extract pathname and remove trailing slash for consistency
            const pathname = parsedUrl.pathname.replace(/\/$/, '')

            // Check for language parameter for internationalized pages
            const lang = parsedUrl.searchParams.get('lang')

            /**
             * Generate comprehensive slug variants for revalidation
             *
             * For non-localized pages: ["/path", "/path/"]
             * For localized pages: ["/path", "/path/lang", "/path/", "/path/lang/"]
             *
             * This ensures all possible cached variations are invalidated
             */
            const slugs = lang
                ? [`${pathname}`, `${pathname}/${lang}`, `${pathname}/`, `${pathname}/${lang}/`]
                : [`${pathname}`, `${pathname}/`]

            // Call page revalidation API with generated slugs
            const response = await fetch(`${vercelUrl}/api/revalidate/page/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ slugs }),
            })
            const data = await response.json()

            // Handle revalidation response
            if (data.revalidated) {
                setSuccess('Page updated successfully. Reloading...')
                console.log('Page revalidation succeeded', data)
                // Reload page to show fresh content after cache invalidation
                window.location.reload()
            } else {
                setError('Page revalidation failed')
                console.error('Page revalidation failed', data)
            }
        } catch (err) {
            setError('Page revalidation failed')
            console.error('Page revalidation failed', err)
        } finally {
            // Reset loading state regardless of success/failure
            setLoading(false)
        }
    }

    /**
     * Render floating button with status messages
     *
     * UI Components:
     * - Error message banner (red background)
     * - Success message banner (green background)
     * - Circular flush button (fixed position, bottom-left)
     *
     * Visual States:
     * - Normal: Green button with "Flush Page" text
     * - Loading: Gray button with "..." text and disabled state
     * - Success/Error: Colored message banners with auto-dismiss
     */
    return (
        <>
            {/* Error Message Banner */}
            {error && (
                <div
                    style={{
                        color: '#fff',
                        backgroundColor: '#dc3545', // Bootstrap danger red
                        padding: '10px',
                        borderRadius: '5px',
                        textAlign: 'center',
                    }}
                >
                    {error}
                </div>
            )}

            {/* Success Message Banner */}
            {success && (
                <div
                    style={{
                        color: '#fff',
                        backgroundColor: '#28a745', // Bootstrap success green
                        padding: '10px',
                        borderRadius: '5px',
                        textAlign: 'center',
                    }}
                >
                    {success}
                </div>
            )}

            {/* Floating Button Container */}
            <div
                style={{
                    position: 'fixed',
                    bottom: '20px',
                    left: '20px',
                    zIndex: 1000, // High z-index to appear above other content
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '10px',
                }}
            >
                {/* Cache Flush Button */}
                <button
                    onClick={FlushPage}
                    disabled={loading}
                    style={{
                        backgroundColor: loading ? '#ccc' : '#56e5a9', // Gray when loading, green when ready
                        color: 'black',
                        border: '1px dashed #54661d',
                        borderRadius: '50%', // Circular button
                        width: '50px',
                        height: '50px',
                        fontSize: '12px',
                        cursor: loading ? 'not-allowed' : 'pointer',
                        boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.2)', // Subtle shadow
                        transition: 'transform 0.2s ease', // Smooth scaling animation
                        transform: loading ? 'scale(0.95)' : 'scale(1)', // Slight scale down when loading
                    }}
                >
                    {loading ? '...' : 'Flush Page'}
                </button>
            </div>
        </>
    )
}

export default FlushPageFloatingButton