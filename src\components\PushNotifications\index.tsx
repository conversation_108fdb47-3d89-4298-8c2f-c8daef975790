'use client'

import { getMessaging, isSupported, onMessage } from 'firebase/messaging'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect } from 'react'
import firebaseApp from '../../globals/firebase'
import { isSafari } from '../../globals/utils'
import {
  FetchNotifications,
  clearTempNotifications,
  setIsAudioPlay,
  setIsNewNotification,
  setIsNotificationCenterOpen,
  setIsSafari,
  setIsSaveTempNotis,
  setLocalPushNotifications,
  setNotificationPermission,
  setTempNotifications,
} from '../../redux/slices/notificationCenterSlice'
import { useAppDispatch, useAppSelector } from '../../redux/store'
import CustomNotification from '../NotificationCenter/CustomNotification'
import {
  CheckIsNotiCategoryAllowed,
  NotificationFromFirebase,
  fetchNotificationsFromStorage,
  saveNotificationsToStorage,
} from './helpers'
import styles from './index.module.scss'

/**
 * Handles real-time live notifications from Firebase.
 *
 * This component listens for incoming notifications using Firebase messaging,
 * processes them based on user preferences, and displays them as push notifications.
 * It manages the local state of notifications, updates Redux stores as necessary,
 * and handles user interactions with the notifications.
 *
 * The component performs the following actions:
 * - Listens for new notifications from Firebase and processes them.
 * - Checks user preferences to determine if notifications should be displayed.
 * - Manages the storage of notifications in local and temporary state.
 * - Dispatches actions to update the notification center's state and UI.
 * - Handles user interactions such as closing and clicking notifications.
 */

function RealTimeLiveNotification() {
  const domain = process.env.NEXT_PUBLIC_DOMAIN

  // const isTabActive = useTabFocus()

  const dispatch = useAppDispatch()

  const {
    isNotificationCenterOpen,
    tempNotifications,
    isSaveTempNotis,
    notificationPermission,
    localPushNotifications,
  } = useAppSelector((state) => state?.notiCenter)

  /**
   * Handles the closing of a notification and updates notification storage accordingly.
   *
   * If the notification is a system notification and dismissable, it is moved to the 'previous' notifications storage.
   * For regular notifications, it checks if the 'Read More' was clicked. If so, the notification is stored in 'previous',
   * otherwise, it is stored in 'latest'. It also updates the notification analytics based on the action.
   *
   * @param {any} noti - The notification object to be processed.
   * @param {boolean} [isReadMoreClick] - Optional flag indicating if the 'Read More' button was clicked.
   */

  const handleOnCloseNotification = (noti: any, isReadMoreClick?: boolean) => {
    if (noti?.isSystemNotification === 'true') {
      if (noti?.isDismissable === 'true') {
        let oldLatestNotifications = fetchNotificationsFromStorage('previous')
        oldLatestNotifications = [noti, ...oldLatestNotifications]
        saveNotificationsToStorage('previous', oldLatestNotifications)
      }
    } else {
      // handle close notification
      let oldLatestNotifications: any = []

      // if read more click then save to previous as user has clicked on read more
      if (isReadMoreClick) {
        oldLatestNotifications = fetchNotificationsFromStorage('previous')
      } else {
        oldLatestNotifications = fetchNotificationsFromStorage('latest')
      }
      oldLatestNotifications = [noti, ...oldLatestNotifications]

      if (isReadMoreClick) {
        saveNotificationsToStorage('previous', oldLatestNotifications)
        dispatch(setIsNewNotification(false))
      } else {
        saveNotificationsToStorage('latest', oldLatestNotifications)
      }

      // remove from temp notifications
      dispatch(FetchNotifications(domain))
      sendNotificationAnalytics(isReadMoreClick ? 'click' : 'close', noti)
    }
    const filteredNotifications = localPushNotifications?.filter(
      (item: any) => item?.id !== noti?.id
    )
    dispatch(setLocalPushNotifications(filteredNotifications))
    dispatch(setTempNotifications(filteredNotifications))
  }

  /**
   * Generates a random unique identifier of the given length.
   * @param {number} [length=16] - The length of the unique identifier to generate.
   * @returns {string} A random unique identifier of the given length.
   */
  function generateUniqueId(length = 16) {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * Returns the value of a cookie with the given name, or null if no such cookie exists.
   * @param {string} name - The name of the cookie to retrieve.
   * @returns {string | null} The value of the cookie, or null if no such cookie exists.
   */
  const getCookieValue = (name: string): string | null => {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop()?.split(';').shift() ?? null
    return null
  }

  /**
   * Sends notification analytics to the server.
   * @param {string} eventType - The type of event to log, either 'click' or 'close'.
   * @param {any} notification - The notification object containing the data to log.
   *
   * The function sends a POST request to the '/api/send-notification-analytics' endpoint
   * with a JSON payload containing the client_id, event name, and event params.
   * The event name is in the format 'notification_<eventType>', and the params object
   * contains the title, description, type, and url of the notification.
   *
   * If the request fails, an error is logged to the console.
   */
  async function sendNotificationAnalytics(
    eventType: 'click' | 'close',
    notification: any
  ) {
    // console.log('notification: ', { eventType, notification })
    /**
     * Push Notification Analytics Tracking
     *
     * Tracks user interactions with push notifications (clicks and dismissals)
     * using GA4 Measurement Protocol. This provides insights into notification
     * effectiveness and user engagement patterns.
     */

    // Map event types to analytics event names
    let eventString = {
      click: 'click',
      close: 'close',
    }

    // Construct GA4 Measurement Protocol payload
    let payload = {
      // Use GA4 client ID from cookie, or generate unique ID for tracking
      client_id: getCookieValue('_ga') || generateUniqueId(),
      events: [
        {
          // Event name format: notification_click or notification_close
          name: `notification_${eventString[eventType]}`,
          params: {
            title: notification?.title,
            description: notification?.body,
            // Distinguish between page-only and browser notifications
            type:
              notification?.type === 'both' ? 'Page & Browser' : 'Only Browser',
            url: notification?.data?.url || '',
          },
        },
      ],
    }
    // Debug logging (commented out for production)
    // console.log('payload: ', JSON.stringify(payload))

    try {
      // Send notification analytics to GA4 via API route
      await fetch('/api/send-notification-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })
    } catch (error) {
      console.error('Error sending analytics:', error)
    }
  }

  /**
   * Handles the reception of a new notification from Firebase.
   *
   * This function processes incoming notifications, checks user preferences,
   * and updates the state accordingly. It determines if a notification should
   * be displayed based on its category and system notification status.
   *
   * The function performs the following actions:
   * - Checks if the notification category is allowed by user preferences or if it's a system notification.
   * - Updates and dispatches the notification to temporary storage.
   * - Sets flags to indicate new notifications and triggers audio alerts.
   * - If the notification center is open, it closes the notification to save it in the latest updates.
   * - Otherwise, it displays the notification and updates the local push notifications list.
   *
   * @param notification - An object representing the notification received from Firebase.
   */

  const handleNewNotificationFromFirebase = (
    notification: NotificationFromFirebase
  ) => {
    // const notiTimeStamp = new Date(notification?.timeStamp || '')
    // const fiveMinutesAgo = Date.now() - 1 * 60 * 1000
    // const isNotiExpired = notiTimeStamp.getTime() < fiveMinutesAgo

    const isSysNoti = notification?.isSystemNotification === 'true'

    // check for user preferences category
    const isCategoryAllowedOrIsSysNoti =
      CheckIsNotiCategoryAllowed({
        payloadCategory: JSON.parse(notification?.category || '[]'),
      }) || isSysNoti

    if (!isCategoryAllowedOrIsSysNoti) return

    const updatedNotifications = {
      ...notification,
      isPersistent: notification?.duration === '0',
    }
    dispatch(setTempNotifications(notification))
    dispatch(setIsNewNotification(true))
    dispatch(setIsAudioPlay(true))

    // if notification center is open then close the notification so it will be saved in latest updates else show as slide in notification
    if (isNotificationCenterOpen && !isSysNoti) {
      handleOnCloseNotification(notification)
    } else {
      dispatch(setIsNotificationCenterOpen(false))

      let oldLocalPushNotifications = [
        updatedNotifications,
        ...localPushNotifications,
      ]
      dispatch(setLocalPushNotifications(oldLocalPushNotifications))
    }
  }

  /**
   * Function to handle when a notification is clicked.
   * It sets `isSaveTempNotis` to true and then sets it to false after 1 second.
   * It also clears the localPushNotifications array.
   */
  const handleOnClickNotification = () => {
    dispatch(setIsSaveTempNotis(true))
    setTimeout(() => {
      dispatch(setIsSaveTempNotis(false))
      dispatch(setLocalPushNotifications([]))
    }, 1000)
  }

  useEffect(() => {
    if (isSaveTempNotis) {
      // if user click on notification body then save all temp notifications to latest updates
      let oldLatestNotifications = fetchNotificationsFromStorage('latest')

      let oldPreviousNotifications = fetchNotificationsFromStorage('previous')

      tempNotifications?.forEach((element: any) => {
        if (element.isSystemNotification === 'true') {
          if (element.isDismissable === 'true') {
            oldPreviousNotifications = [element, ...oldPreviousNotifications]
          }
        } else {
          oldLatestNotifications = [element, ...oldLatestNotifications]
          sendNotificationAnalytics('close', element)
        }
      })

      saveNotificationsToStorage('latest', oldLatestNotifications)
      saveNotificationsToStorage('previous', oldPreviousNotifications)
      dispatch(clearTempNotifications())
      dispatch(setIsNotificationCenterOpen(true))
      dispatch(setLocalPushNotifications([]))
    }
  }, [isSaveTempNotis])

  useEffect(() => {
    // listen to new notification from firebase
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      const messaging = getMessaging(firebaseApp)

      const unsubscribe = onMessage(messaging, (payload: any) => {
        if (
          payload?.data?.type &&
          payload?.data?.type !== 'browser' &&
          notificationPermission === 'granted'
        )
          handleNewNotificationFromFirebase(payload?.data)
      })

      return () => unsubscribe()
    }
  }, [isNotificationCenterOpen, notificationPermission, localPushNotifications])

  const saveNotificationPermission = async () => {
    // save notification permission to local storage
    const isBrowserSupported = await isSupported()
    if (isBrowserSupported)
      dispatch(setNotificationPermission(Notification?.permission))
  }

  useEffect(() => {
    // check if safari then set isSafari to true and will be used when user allow notification
    if (isSafari()) dispatch(setIsSafari(true))
    saveNotificationPermission()
  }, [])

  return (
    <>
      {localPushNotifications.length > 0 && (
        <div className={styles.pushNotificationsContainer}>
          {localPushNotifications.map(
            (noti: NotificationFromFirebase, index: number) => (
              <AnimatePresence key={noti.id} mode='wait'>
                <motion.div
                  key={noti.id}
                  id={`push-noti-${noti.id}`}
                  initial={{ x: '100%', opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{
                    opacity: 0,
                    height: 0,
                    marginBottom: 0,
                    padding: 0,
                  }}
                  transition={{
                    duration: 0.4,
                    ease: 'easeInOut',
                  }}
                >
                  <CustomNotification
                    {...noti}
                    isPushNotification={true}
                    isSysNotification={noti?.isSystemNotification === 'true'}
                    handleClose={({
                      isReadMoreClick,
                    }: {
                      isReadMoreClick?: boolean
                    }) => handleOnCloseNotification(noti, isReadMoreClick)}
                    handleClick={() => handleOnClickNotification()}
                  />
                </motion.div>
              </AnimatePresence>
            )
          )}
        </div>
      )}
    </>
  )
}

export default RealTimeLiveNotification
