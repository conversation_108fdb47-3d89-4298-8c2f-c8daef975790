# Algolia Search Implementation Documentation

## Overview

This document provides a comprehensive overview of the Algolia search implementation in the 17.studio codebase. The system provides full-text search capabilities across website content, with features including instant search, recommendations, recent search history, and analytics tracking.

## Architecture

### Core Components

The Algolia implementation consists of several key components:

1. **Search Client Configuration** - Centralized Algolia client setup
2. **Search UI Components** - React components for search interface
3. **Data Indexing System** - Automated content synchronization with Algolia
4. **Recommendation Engine** - AI-powered content recommendations
5. **State Management** - Redux-based search state handling
6. **Analytics Integration** - Search event tracking

## Configuration

### Environment Variables

The system requires the following environment variables:

```env
NEXT_PUBLIC_ALGOLIA_APPLICATION_ID=your_app_id
NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY=your_search_key
ALGOLIA_ADMIN_KEY=your_admin_key
NEXT_PUBLIC_ALGOLIA_INDEX=your_index_name
NEXT_PUBLIC_ALGOLIA_SYNC_ALLOWDED=true
```

### Client Configuration

**Location**: `src/globals/algolia-configuration.ts`

```typescript
export const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)

export const recommendClient = recommend(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)
```

## Data Indexing

### Automated Sync Process

**Location**: `src/algolia/algolia-sync.js`

The indexing system automatically syncs content from Contentful to Algolia:

#### Build-Time Indexing

- Runs automatically during the build process (`yarn build`)
- Configured in `package.json`: `"build": "yarn prebuild && next build && node src/algolia/algolia-sync.js"`

#### Manual Sync API

- **Endpoint**: `POST /api/sync-algolia-index`
- **Authentication**: Requires `x-api-key` header
- **Usage**: Manual content synchronization

### Data Sources

#### Primary Content Query

```graphql
pageCollection(
  limit: 1000
  preview: false
  where: {
    hideFromAlgolia_not: true
    contentfulMetadata: { tags: { id_contains_all: "${DOMAIN}" } }
  }
) {
  items {
    sys { id }
    title
    slug
    shortTitle
    seoDescription
    seoKeywords
    pageThumbnail { url }
  }
}
```

#### Experimentation Pages

- Handles A/B testing pages separately
- Maps experimental page IDs to master page slugs
- Ensures consistent search results across variants

### Indexed Fields

Each Algolia record contains:

- `objectID` - Unique identifier (Contentful sys.id)
- `title` - Page title
- `slug` - URL slug
- `shortTitle` - Abbreviated title for display
- `seoDescription` - Meta description
- `seoKeywords` - SEO keywords
- `pageThumbnail.url` - Featured image URL

## Search Components

### SimpleSearch Component

**Location**: `src/components/SystemSearch/SimpleSearch/index.tsx`

Main search interface component that provides:

- Instant search functionality
- Toggle between default and active search states
- Integration with React InstantSearch

### SearchInput Component

**Location**: `src/components/Controls/SearchInput/index.tsx`

Search input field with features:

- Real-time query processing with 1-second debounce
- Domain-specific placeholder text
- Clear functionality
- Analytics event tracking
- Redux state integration

### SearchActive Component

**Location**: `src/components/SystemSearch/SearchActive/index.tsx`

Active search results display:

- Real-time search results from Algolia
- Content filtering (excludes insights/events from main results)
- Recommendation integration
- Recent search management
- Categorized results display

### SearchDefault Component

**Location**: `src/components/SystemSearch/SearchDefault/index.tsx`

Default search state showing:

- Recent search history (last 5 searches)
- Personalized recommendations based on search history
- Domain-specific content filtering

## Recommendation System

### Implementation

- Uses `@algolia/recommend` and `@algolia/recommend-react`
- Provides personalized content suggestions
- Based on user search history and behavior

### Features

- **Related Products**: Content similar to previously viewed items
- **Domain Filtering**: Excludes specific domains from recommendations
- **Content Categorization**: Separates events, webinars, and insights

### Excluded Domains

```typescript
const excludedDomains = [
  'verifino.com',
  'financeactive.com',
  'one11advisors.com',
]
```

## State Management

### Redux Store Structure

**Location**: `src/redux/slices/searchSlice.ts`

```typescript
interface SearchState {
  isSearchActive: boolean // Search UI visibility
  recentSearch: object // Current search context
  rePopulate: boolean // Search result refresh flag
}
```

### Actions

- `setSearchState(boolean)` - Toggle search interface
- `setRecentSearchReducer(object)` - Update recent search data
- `setRePopulateReducer(boolean)` - Control result refresh

### Local Storage Integration

- Recent searches stored in `altusSearchHistory`
- Automatic deduplication
- Maximum 5 recent searches displayed
- Persistent across browser sessions

## Search Utilities

### URL Processing

**Location**: `src/components/SystemSearch/searchUtil.ts`

```typescript
export const getHref = (href: any) => {
  let url = ''
  if (href.charAt(0) === '/') url = href
  else url = '/' + href
  return url
}
```

### Result Formatting

Transforms Algolia hits into component-ready data structures with:

- Standardized card layouts
- Consistent CTA formatting
- Image optimization
- Content excerpts

## Content Filtering

### Search Result Filtering

**Location**: `src/components/SystemSearch/SearchActive/filterRecommendation.ts`

#### Exclusion Filters

```typescript
export function exludeInsightsAndEventsFilter(hit) {
  return !(
    hit?.slug?.startsWith('events') ||
    hit?.slug?.startsWith('webinars') ||
    hit?.slug?.startsWith('insights') ||
    hit?.slug?.startsWith('featured-insights')
  )
}
```

#### Content Categorization

- **Events & Webinars**: Filtered and displayed separately
- **Insights & Research**: Grouped for specialized display
- **General Content**: Main search results

## Analytics Integration

### Event Tracking

**Location**: `src/utils/analyticsEvents.ts`

#### Search Events

- `enteredSearchInputEvent()` - User starts typing
- `searchResultClickEvent(href)` - User clicks search result
- `buttonClickEvent()` - General interaction tracking

#### Implementation

```typescript
export function enteredSearchInputEvent() {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'entered_search_input',
    })
  }
}
```

## Performance Optimizations

### Client-Side Optimizations

- **Debounced Search**: 1-second delay prevents excessive API calls
- **Lazy Loading**: Components load on demand
- **Result Limiting**: Maximum 5 results per category
- **Local Caching**: Recent searches cached locally

### Build-Time Optimizations

- **Lite Client**: Uses `algoliasearch/lite` for smaller bundle size
- **Environment Checks**: Sync only runs in production or when explicitly allowed
- **Batch Processing**: Bulk index updates for efficiency

## Error Handling

### Sync Process

- Graceful fallback when sync fails
- Detailed error logging
- Retry mechanism for rate limiting
- Environment-based execution control

### Search Interface

- Fallback to empty state when no results
- Error boundary protection
- Graceful degradation without search functionality

## Security

### API Key Management

- Separate keys for search (public) and admin (private) operations
- Environment-based configuration
- Secure API endpoint with authentication

### Content Security

- Domain-based content filtering
- Preview/published content separation
- Tag-based access control through Contentful

## Dependencies

### Core Packages

- `algoliasearch`: ^4.20.0
- `@algolia/recommend`: ^4.20.0
- `@algolia/recommend-react`: ^1.10.0
- `react-instantsearch`: Latest
- `instantsearch.js`: 4.78.3

### Integration Dependencies

- Redux Toolkit for state management
- React hooks for component logic
- Next.js API routes for sync endpoints

## Functionality

### Core Search Features

#### 1. Instant Search

- **Real-time Results**: Search results update as user types
- **Debounced Input**: 1-second delay prevents excessive API calls
- **Multi-field Search**: Searches across title, description, and keywords
- **Typo Tolerance**: Algolia's built-in typo tolerance for better UX

#### 2. Search States

- **Default State**: Shows recent searches and recommendations
- **Active State**: Displays live search results and categorized content
- **Empty State**: Graceful handling when no results found

#### 3. Content Categorization

- **Main Results**: General pages and content
- **Events & Webinars**: Filtered separately for specialized display
- **Insights & Research**: Academic and analytical content
- **Excluded Content**: Certain content types filtered from main results

#### 4. Recent Search History

- **Local Storage**: Persistent search history across sessions
- **Deduplication**: Prevents duplicate entries
- **Limited History**: Shows last 5 searches
- **Click Tracking**: Analytics on historical search interactions

### Advanced Features

#### 1. Personalized Recommendations

- **Behavior-Based**: Recommendations based on search history
- **Related Content**: Similar pages to previously viewed content
- **Domain Filtering**: Excludes irrelevant domains
- **Contextual Suggestions**: Content relevant to current search context

#### 2. Multi-Domain Support

- **Domain-Specific Search**: Search scoped to current domain
- **Cross-Domain Filtering**: Excludes content from other business units
- **Localized Results**: Content filtered by domain tags

#### 3. Experimentation Integration

- **A/B Testing Support**: Handles experimental page variants
- **Master Page Mapping**: Maps experiment pages to canonical URLs
- **Consistent Results**: Ensures search results remain stable across variants

#### 4. Content Management Integration

- **Contentful Sync**: Automatic synchronization with Contentful CMS
- **Tag-Based Filtering**: Uses Contentful tags for content organization
- **Preview Handling**: Separate handling for draft vs. published content
- **Hide from Search**: Respects `hideFromAlgolia` flag in Contentful

### Search Result Processing

#### 1. Data Transformation

```typescript
const transformToAlgoliaRecords = (response) => {
  return response?.data?.pageCollection?.items
    ?.map(({ sys, ...rest }) =>
      sys?.id ? { objectID: sys.id, ...rest } : null
    )
    ?.filter(Boolean)
}
```

#### 2. Result Formatting

- **Card Layout**: Consistent card-based result display
- **Image Optimization**: Thumbnail processing for search results
- **Content Excerpts**: Truncated descriptions for better UX
- **CTA Standardization**: Consistent call-to-action formatting

#### 3. URL Processing

- **Slug Normalization**: Ensures proper URL formatting
- **Domain Prefixing**: Adds domain context to relative URLs
- **Link Validation**: Validates and processes result links

### Analytics and Tracking

#### 1. Search Analytics

- **Search Input Tracking**: Monitors when users start searching
- **Result Click Tracking**: Tracks which results users interact with
- **Search Abandonment**: Identifies when users leave without clicking
- **Popular Queries**: Tracks most common search terms

#### 2. Performance Metrics

- **Search Response Time**: Monitors Algolia API response times
- **Index Size**: Tracks number of indexed documents
- **Search Volume**: Monitors search frequency and patterns
- **Error Rates**: Tracks failed searches and API errors

#### 3. User Behavior Analysis

- **Search Patterns**: Analyzes common search workflows
- **Content Discovery**: Tracks how users find content
- **Recommendation Effectiveness**: Measures recommendation click-through rates
- **Search Refinement**: Monitors how users modify searches

### Technical Implementation Details

#### 1. Component Architecture

```
SimpleSearch (Main Container)
├── SearchInput (Input Field)
├── SearchActive (Live Results)
│   ├── MenuListMegamenu (Quick Links)
│   ├── CardGenericSearch (Result Cards)
│   └── SimpleParagraph (Category Headers)
└── SearchDefault (Default State)
    ├── Recent Searches List
    └── Recommendation Cards
```

#### 2. State Flow

1. User types in SearchInput
2. Redux state updates (`setSearchState`, `setRePopulateReducer`)
3. Algolia query triggered with debounce
4. Results processed and filtered
5. UI updates with SearchActive component
6. Analytics events fired
7. Recent searches updated in localStorage

#### 3. Data Flow

```
Contentful CMS → GraphQL Query → Algolia Sync → Algolia Index
                                                      ↓
User Search → React InstantSearch → Algolia API → Search Results
                                                      ↓
Result Processing → Component Rendering → User Interaction → Analytics
```

### Error Handling and Resilience

#### 1. Sync Process Resilience

- **Environment Checks**: Only syncs in appropriate environments
- **Rate Limit Handling**: Automatic retry with exponential backoff
- **Partial Sync Recovery**: Handles partial sync failures gracefully
- **Logging**: Comprehensive error logging for debugging

#### 2. Search Interface Resilience

- **Fallback States**: Graceful degradation when search unavailable
- **Network Error Handling**: Handles API timeouts and failures
- **Empty Result Handling**: User-friendly empty state messaging
- **Component Error Boundaries**: Prevents search errors from breaking page

#### 3. Data Integrity

- **Validation**: Validates data before indexing
- **Deduplication**: Prevents duplicate content in index
- **Content Filtering**: Ensures only appropriate content is indexed
- **Schema Consistency**: Maintains consistent data structure

## Maintenance and Monitoring

### Regular Maintenance Tasks

1. **Index Health Monitoring**: Regular checks on index size and performance
2. **Content Audit**: Periodic review of indexed content accuracy
3. **Performance Optimization**: Regular analysis of search performance metrics
4. **Security Review**: Periodic review of API key security and access controls

### Troubleshooting Common Issues

1. **Sync Failures**: Check environment variables and API connectivity
2. **Missing Results**: Verify content tags and `hideFromAlgolia` flags
3. **Performance Issues**: Review query complexity and result limits
4. **Analytics Gaps**: Verify GTM integration and event firing

### Monitoring Dashboards

- **Algolia Dashboard**: Native Algolia analytics and performance metrics
- **Google Analytics**: Search behavior and conversion tracking
- **Application Logs**: Error tracking and performance monitoring
- **Content Audit Reports**: Regular content freshness and accuracy reports
