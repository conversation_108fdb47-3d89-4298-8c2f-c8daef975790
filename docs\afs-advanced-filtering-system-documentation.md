# AFS (Advanced Filtering System) Documentation

## Description / Overview

The Advanced Filtering System (AFS) is a comprehensive content filtering and display solution built for the Altus Group website. It provides dynamic, tag-based filtering capabilities across multiple content types including insights, events, customer stories, project work, press releases, and expert profiles. The system features an interactive map component and sophisticated multi-level filtering with real-time URL synchronization and analytics tracking.

### Key Features

- **Multi-Template Support**: 7 different content templates (Insights, Events, Customer Stories, Project Work, Press Release, Experts, Map)
- **Dynamic Filtering**: Tag-based filtering with multi-level dropdown support
- **Real-time Updates**: Live filtering with URL synchronization
- **Interactive Map**: Geographic visualization with CSV data integration
- **Analytics Integration**: Comprehensive event tracking for user interactions
- **Responsive Design**: Mobile-optimized filtering interface
- **Internationalization**: Multi-language support with locale-based content

## Contributors

### Development Team

- **Frontend Engineers**: React/Next.js specialists
- **Backend Engineers**: Contentful CMS integration specialists
- **UX/UI Designers**: System filtration interface designers
- **Analytics Engineers**: GA4 event tracking implementation
- **DevOps Engineers**: Vercel deployment and optimization

### Stakeholders

- **Content Managers**: Contentful content creation and tagging
- **Marketing Team**: Campaign tracking and analytics requirements
- **Product Managers**: Feature requirements and user experience optimization

## Functionality

### Core Components

#### 1. AFS Router (`AFSRouter.tsx`)

Central routing component that determines which AFS template to render based on configuration.

**Supported Templates:**

- `Insights` - Blog posts and thought leadership content
- `Customer Stories` - Case studies and success stories
- `Project Work` - Portfolio and project showcases
- `Experts` - Team member profiles and expertise
- `Press Release` - News and press announcements
- `Events` - Webinars and event listings
- `Map` - Interactive geographic visualization

#### 2. System Filtration (`SystemFilteration`)

Advanced filtering interface with multiple interaction modes:

**Filter Types:**

- **Single-select Dropdowns**: Category-based filtering
- **Multi-select Dropdowns**: Multiple option selection
- **Multi-level Dropdowns**: Hierarchical filtering (Country → City)
- **Tag-based Filtering**: Dynamic tag management
- **Search Integration**: Text-based content filtering

#### 3. Content Templates

##### Insights Template

- **Purpose**: Display blog posts, whitepapers, and thought leadership
- **Features**: Date sorting, category filtering, featured content highlighting
- **Analytics**: `afs_menu` events, content engagement tracking

##### Events Template

- **Purpose**: Webinar and event management
- **Features**: Date-based filtering, registration tracking, timezone handling
- **Integration**: Calendar systems, registration platforms

##### Customer Stories Template

- **Purpose**: Case study and success story showcase
- **Features**: Industry filtering, solution categorization, impact metrics
- **Analytics**: Story engagement and conversion tracking

##### Project Work Template

- **Purpose**: Portfolio and project showcase
- **Features**: Project type filtering, geographic filtering, timeline sorting
- **Integration**: Project management systems

##### Experts Template

- **Purpose**: Team member and expert profiles
- **Features**: Expertise filtering, location-based search, contact integration
- **Analytics**: Expert profile engagement tracking

##### Press Release Template

- **Purpose**: News and press announcement management
- **Features**: Date filtering, topic categorization, media asset integration
- **Integration**: Media distribution platforms

##### Map Template

- **Purpose**: Interactive geographic data visualization
- **Features**: CSV data integration, location-based filtering, rich content overlays
- **Technology**: Google Maps API, D3.js for data visualization

### Data Flow Architecture

#### 1. Content Management

```
Contentful CMS → GraphQL API → AFS Router → Template Components
```

#### 2. Filtering Process

```
User Interaction → Redux State → Filter Functions → Content Re-render → URL Update
```

#### 3. Analytics Flow

```
User Action → Event Tracking → GA4 Analytics → Dashboard Reporting
```

### Filter Logic Implementation

#### Simple Filtering (`keyMatchF`)

- Matches content tags against selected filter values
- Boolean logic for inclusion/exclusion
- Performance optimized for large datasets

#### Multi-level Filtering (`multiLevelMatchF`)

- Hierarchical filtering (e.g., Country → City)
- Dependent dropdown relationships
- Complex boolean logic for nested conditions

#### Filter State Management

- **Redux Integration**: Centralized state management
- **URL Synchronization**: Browser history and bookmarkable URLs
- **Local Storage**: Filter preference persistence
- **Session Management**: Temporary filter state handling

## UI Components

### Filter Interface Components

#### 1. Multi-select Dropdown

- **Purpose**: Multiple option selection
- **Features**: Search within options, select all/none, visual indicators
- **Accessibility**: ARIA labels, keyboard navigation

#### 2. Multi-level Dropdown

- **Purpose**: Hierarchical filtering (Country/City)
- **Features**: Dependent options, cascading updates, geographic data integration
- **Data Source**: Contentful tags, CSV data files

#### 3. Tag Display System

- **Purpose**: Visual representation of active filters
- **Features**: Remove individual tags, clear all functionality, count indicators
- **Styling**: Consistent with design system, responsive layout

#### 4. Filter Results Display

- **Purpose**: Show filtered content count and results
- **Features**: Real-time updates, loading states, empty state handling
- **Internationalization**: Localized result text and formatting

### Content Display Components

#### 1. Card-based Layouts

- **Insights Cards**: Featured images, excerpts, read more CTAs
- **Event Cards**: Date/time display, registration buttons, calendar integration
- **Expert Cards**: Profile photos, expertise tags, contact information
- **Project Cards**: Portfolio images, project details, case study links

#### 2. List-based Layouts

- **Press Release List**: Chronological display, media assets, sharing options
- **Search Results**: Relevance-based ordering, snippet previews

#### 3. Map Interface

- **Interactive Map**: Zoom controls, marker clustering, info windows
- **Location Filters**: Geographic boundary selection, city/country filters
- **Data Overlays**: Rich content integration, multimedia support

## Codebase Integration

### File Structure

```
src/
├── systems/AFS/
│   ├── AFSRouter.tsx                 # Main routing component
│   ├── AFSPages/                     # Template components
│   │   ├── Insights/
│   │   ├── Events/
│   │   ├── CustomerStories/
│   │   ├── ProjectWork/
│   │   ├── PressRelease/
│   │   ├── OurExperts/
│   │   └── utils.ts
│   ├── AFSMap/                       # Map component
│   │   ├── index.tsx
│   │   └── afsMap.mapping.ts
│   ├── AFSQuery/                     # GraphQL queries
│   │   └── afs.query.ts
│   └── lib/                          # Utility functions
│       ├── filters.ts
│       ├── filterFunctions.ts
│       └── copyFilters/
├── components/SystemFilteration/     # Filter UI components
│   ├── index.tsx
│   ├── interface.ts
│   └── @Core/
├── redux/slices/filterSlice/         # State management
│   ├── filtersSlice.ts
│   └── utils.ts
└── utils/analyticsEvents.ts          # Analytics tracking
```

### Key Dependencies

- **React 18+**: Component framework
- **Next.js 14**: Application framework
- **Redux Toolkit**: State management
- **Contentful**: Headless CMS
- **GraphQL**: Data fetching
- **Framer Motion**: Animations
- **Moment.js**: Date handling
- **Papa Parse**: CSV processing
- **D3.js**: Data visualization
- **Google Maps API**: Map functionality

### Integration Points

#### 1. Contentful CMS Integration

- **Content Types**: SystemAfs, Page, ComponentRichtext
- **Tag Management**: Hierarchical tagging system
- **Asset Management**: Images, documents, CSV files
- **Preview Mode**: Draft content preview capability

#### 2. Redux State Management

- **Filter State**: `filterSlice` for active filters
- **Tag Mapping**: `tagMapSlice` for tag ID to name mapping
- **UI State**: Loading states, error handling

#### 3. Analytics Integration

- **Event Tracking**: Custom GA4 events for filter interactions
- **Performance Monitoring**: Filter operation timing
- **User Behavior**: Filter usage patterns and preferences

## Vercel Deployment

### Environment Configuration

```bash
# Contentful Configuration
CONTENTFUL_SPACE_ID=your_space_id
CONTENTFUL_ACCESS_TOKEN=your_access_token
CONTENTFUL_PREVIEW_ACCESS_TOKEN=your_preview_token
NEXT_PUBLIC_PREVIEW=false

# Analytics Configuration
NEXT_PUBLIC_GA4_MEASUREMENT_ID=your_measurement_id
GA4_API_SECRET=your_api_secret

# Google Maps Configuration
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_maps_api_key
```

### Build Configuration

- **Node.js Version**: 18.x
- **Build Command**: `yarn build`
- **Output Directory**: `.next`
- **Environment**: Production optimizations enabled

### Performance Optimizations

- **Static Generation**: Pre-built pages for common filter combinations
- **Incremental Static Regeneration**: Dynamic content updates
- **Edge Functions**: Filter processing optimization
- **CDN Integration**: Asset delivery optimization

### Deployment Pipeline

1. **Code Push**: GitHub repository update
2. **Automatic Build**: Vercel build trigger
3. **Content Sync**: Contentful webhook integration
4. **Cache Invalidation**: Filter-specific cache clearing
5. **Analytics Validation**: Event tracking verification

## GitHub Repository Structure

### Branch Strategy

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/afs-\***: AFS-specific feature branches
- **hotfix/afs-\***: Critical AFS bug fixes

### Key Files and Directories

```
├── .github/workflows/          # CI/CD pipelines
├── src/systems/AFS/           # AFS core system
├── src/components/SystemFilteration/  # Filter components
├── src/redux/slices/filterSlice/      # State management
├── docs/afs-*                 # AFS documentation
├── tests/afs/                 # AFS-specific tests
└── package.json               # Dependencies and scripts
```

### Development Workflow

1. **Feature Branch**: Create from develop
2. **Development**: Local development with hot reload
3. **Testing**: Unit and integration tests
4. **Code Review**: Pull request review process
5. **Integration**: Merge to develop branch
6. **Deployment**: Automatic deployment to staging
7. **Production**: Manual promotion to production

## Contentful Configuration

### Content Models

#### 1. SystemAfs Content Type

```graphql
{
  template: String          # AFS template type
  afsRichtext: RichText    # Additional content
  dataFile: Asset          # CSV data for maps
  afsFiltersCollection: [AfsFilter]  # Filter configurations
  contentfulMetadata: {
    tags: [Tag]            # Content categorization
  }
}
```

#### 2. AfsFilter Content Type

```graphql
{
  heading: String          # Filter section title
  template: String         # Filter type identifier
  afsTagNames: [String]    # Human-readable tag names
  afsTagIds: [String]      # Contentful tag IDs
  isMultiLevel: Boolean    # Hierarchical filter flag
  isLightMode: Boolean     # UI styling variant
}
```

### Tag Management Strategy

- **Hierarchical Structure**: `Category:Subcategory:Item`
- **Naming Convention**: Consistent, descriptive tag names
- **Multi-language Support**: Locale-specific tag variations
- **Content Association**: Automatic tag inheritance

### Content Publishing Workflow

1. **Content Creation**: Authors create content in Contentful
2. **Tag Assignment**: Content tagged with relevant categories
3. **Filter Configuration**: AFS filters updated to include new tags
4. **Preview Testing**: Content preview in staging environment
5. **Publication**: Content published to production
6. **Cache Refresh**: AFS filter cache invalidation

### Webhook Integration

- **Content Updates**: Real-time filter updates on content changes
- **Tag Modifications**: Automatic filter reconfiguration
- **Analytics Events**: Content publication tracking
- **Error Handling**: Webhook failure recovery mechanisms

## Usage Instructions

### For Content Managers

#### 1. Setting Up AFS Content

1. **Create SystemAfs Entry**: Navigate to Contentful and create new SystemAfs content
2. **Select Template**: Choose from available templates (Insights, Events, etc.)
3. **Configure Filters**: Add AfsFilter entries for each filter category
4. **Tag Assignment**: Apply relevant tags to content items
5. **Publish Content**: Make content live for filtering

#### 2. Managing Filter Configuration

```javascript
// Example AfsFilter configuration
{
  heading: "Content Type",
  template: "type",
  afsTagNames: ["Blog Post", "White Paper", "Case Study"],
  afsTagIds: ["blogPost", "whitePaper", "caseStudy"],
  isMultiLevel: false,
  isLightMode: true
}
```

#### 3. Multi-level Filter Setup

```javascript
// Country-City hierarchical filter
{
  heading: "Location",
  template: "country",
  afsTagNames: ["Canada:Toronto", "Canada:Vancouver", "USA:New York"],
  afsTagIds: ["canada:toronto", "canada:vancouver", "usa:newYork"],
  isMultiLevel: true,
  isLightMode: false
}
```

### For Developers

#### 1. Adding New AFS Template

```typescript
// 1. Create new template component
const NewTemplate = (props: any) => {
  // Template implementation
}

// 2. Add to AFSRouter switch statement
case 'New Template':
  TheAfs = (
    <NewTemplate
      afsPageData={afsPageData}
      afsFilterData={props}
      locale={locale}
    />
  )
  break
```

#### 2. Custom Filter Implementation

```typescript
// Custom filter function
export function customFilterFunction(items, filters) {
  return items.filter((item) => {
    // Custom filtering logic
    return customLogic(item, filters)
  })
}
```

#### 3. Analytics Event Integration

```typescript
// Add custom AFS analytics event
export function afsCustomEvent(category: string, action: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'afs_custom_action',
      category: category,
      action: action,
      timestamp: new Date().toISOString(),
    })
  }
}
```

### For End Users

#### 1. Using Filter Interface

1. **Select Filters**: Click dropdown menus to select filter options
2. **Multi-select**: Hold Ctrl/Cmd to select multiple options
3. **Clear Filters**: Use "Clear All" button or individual tag removal
4. **Share Filtered View**: Copy URL to share specific filter combination

#### 2. Map Interface Usage

1. **Navigate Map**: Use zoom and pan controls
2. **Filter by Location**: Select countries/cities from dropdown
3. **View Details**: Click map markers for detailed information
4. **Export Data**: Use export functionality for filtered results

## Limitations

### Technical Limitations

1. **Content Volume**: Optimal performance with <10,000 items per template
2. **Filter Complexity**: Maximum 5 levels of hierarchical filtering
3. **Real-time Updates**: 30-second delay for Contentful webhook processing
4. **Browser Support**: IE11 not supported, modern browsers only
5. **Mobile Performance**: Limited filter options on small screens

### Content Limitations

1. **Tag Hierarchy**: Maximum 3-level tag nesting (Category:Sub:Item)
2. **Filter Count**: Recommended maximum 8 filters per template
3. **CSV Data Size**: Map template limited to 50MB CSV files
4. **Image Assets**: Thumbnail images required for optimal display
5. **Localization**: Manual translation required for filter labels

### Performance Considerations

1. **Initial Load**: First page load may take 2-3 seconds with large datasets
2. **Filter Operations**: Complex multi-level filters may have 500ms delay
3. **Memory Usage**: High memory consumption with multiple active filters
4. **Cache Dependencies**: Filter results cached for 5 minutes
5. **API Rate Limits**: Contentful API limited to 55 requests per second

## Third-party Dependencies

### Core Dependencies

1. **Contentful SDK** (`contentful`)

   - Purpose: CMS data fetching and management
   - Version: Latest stable
   - License: MIT
   - Critical: Yes

2. **Google Maps API** (`@react-google-maps/api`)

   - Purpose: Interactive map functionality
   - Version: ^2.19.2
   - License: Apache 2.0
   - Critical: For Map template only

3. **Papa Parse** (`papaparse`)

   - Purpose: CSV data processing for maps
   - Version: ^5.4.1
   - License: MIT
   - Critical: For Map template only

4. **Moment.js** (`moment`)

   - Purpose: Date formatting and manipulation
   - Version: ^2.29.4
   - License: MIT
   - Critical: Yes

5. **D3.js** (`d3-geo`)
   - Purpose: Geographic data visualization
   - Version: ^3.1.1
   - License: BSD-3-Clause
   - Critical: For Map template only

### Analytics Dependencies

1. **Google Analytics 4**

   - Purpose: User interaction tracking
   - Integration: Custom events via dataLayer
   - Critical: For analytics only

2. **Firebase** (`firebase`)
   - Purpose: Real-time analytics and notifications
   - Version: ^11.5.0
   - License: Apache 2.0
   - Critical: No

### UI Dependencies

1. **Framer Motion** (`framer-motion`)

   - Purpose: Smooth animations and transitions
   - Version: ^11.11.17
   - License: MIT
   - Critical: No (graceful degradation)

2. **Ant Design** (`antd`)
   - Purpose: UI components and notifications
   - Version: ^5.24.0
   - License: MIT
   - Critical: Partially (some components)

## Considerations

### Performance Optimization

1. **Lazy Loading**: Implement progressive loading for large datasets
2. **Virtualization**: Use virtual scrolling for extensive lists
3. **Debouncing**: Implement search input debouncing (300ms)
4. **Caching Strategy**: Implement intelligent filter result caching
5. **Bundle Splitting**: Separate AFS code into dedicated chunks

### Accessibility Compliance

1. **ARIA Labels**: Comprehensive labeling for screen readers
2. **Keyboard Navigation**: Full keyboard accessibility
3. **Color Contrast**: WCAG 2.1 AA compliance
4. **Focus Management**: Proper focus handling in dropdowns
5. **Screen Reader Support**: Optimized for NVDA, JAWS, VoiceOver

### SEO Optimization

1. **URL Structure**: SEO-friendly filter URLs
2. **Meta Tags**: Dynamic meta tags based on filters
3. **Structured Data**: Schema.org markup for filtered content
4. **Canonical URLs**: Prevent duplicate content issues
5. **Sitemap Integration**: Include filtered pages in sitemap

### Security Considerations

1. **Input Validation**: Sanitize all filter inputs
2. **XSS Prevention**: Escape user-generated content
3. **API Security**: Secure Contentful API keys
4. **Rate Limiting**: Implement client-side rate limiting
5. **Content Security Policy**: Strict CSP headers

## Fall Back Plan

### Content Delivery Fallback

1. **Static Content**: Pre-generated static pages for critical content
2. **CDN Fallback**: Cached versions served from CDN
3. **Graceful Degradation**: Basic HTML version without JavaScript
4. **Error Boundaries**: React error boundaries for component failures
5. **Offline Support**: Service worker for offline content access

### Filter System Fallback

1. **Simple Filtering**: Basic category-based filtering without advanced features
2. **Server-side Rendering**: SSR fallback for JavaScript-disabled users
3. **URL-based Filters**: Direct URL navigation for filter states
4. **Manual Navigation**: Traditional menu-based content navigation
5. **Search Integration**: Algolia search as alternative content discovery

### Third-party Service Failures

1. **Contentful Outage**:

   - Cached content served from CDN
   - Static backup content deployment
   - Service status page integration

2. **Google Maps Failure**:

   - Static map images as fallback
   - List-based location display
   - Alternative mapping service integration

3. **Analytics Failure**:
   - Local analytics storage
   - Alternative analytics providers
   - Manual event logging

### Recovery Procedures

1. **Automated Monitoring**: Health checks every 30 seconds
2. **Alert System**: Immediate notifications for system failures
3. **Rollback Strategy**: Automated rollback to last known good state
4. **Manual Override**: Admin panel for emergency content management
5. **Communication Plan**: User notification system for extended outages

## Logic Flowchart

```mermaid
graph TD
    A[User Visits AFS Page] --> B[Load AFS Router]
    B --> C{Determine Template Type}

    C -->|Insights| D[Load Insights Template]
    C -->|Events| E[Load Events Template]
    C -->|Customer Stories| F[Load Customer Stories Template]
    C -->|Project Work| G[Load Project Work Template]
    C -->|Press Release| H[Load Press Release Template]
    C -->|Experts| I[Load Experts Template]
    C -->|Map| J[Load Map Template]

    D --> K[Fetch Content from Contentful]
    E --> K
    F --> K
    G --> K
    H --> K
    I --> K
    J --> L[Load CSV Data]

    K --> M[Initialize Filter System]
    L --> M

    M --> N[Parse URL Parameters]
    N --> O{Filters in URL?}

    O -->|Yes| P[Apply URL Filters]
    O -->|No| Q[Show All Content]

    P --> R[Update Redux State]
    Q --> R

    R --> S[Render Filtered Content]
    S --> T[User Interacts with Filters]

    T --> U[Update Filter State]
    U --> V[Apply Filter Logic]
    V --> W[Update URL]
    W --> X[Track Analytics Event]
    X --> Y[Re-render Content]
    Y --> T

    style A fill:#e1f5fe
    style S fill:#c8e6c9
    style T fill:#fff3e0
    style X fill:#fce4ec
```

## Filter Processing Flow

```mermaid
graph LR
    A[Filter Selection] --> B[Validate Input]
    B --> C[Update Redux State]
    C --> D[Generate Filter Object]
    D --> E{Multi-level Filter?}

    E -->|Yes| F[Apply Hierarchical Logic]
    E -->|No| G[Apply Simple Logic]

    F --> H[Process Parent-Child Relationships]
    G --> I[Match Tags Against Content]
    H --> I

    I --> J[Filter Content Array]
    J --> K[Sort Results]
    K --> L[Update URL Parameters]
    L --> M[Trigger Analytics Event]
    M --> N[Re-render Components]

    style A fill:#e3f2fd
    style D fill:#f3e5f5
    style J fill:#e8f5e8
    style M fill:#fff8e1
```

## Misc. Diagrams

### AFS Component Architecture

```mermaid
graph TB
    subgraph "AFS System Architecture"
        A[AFS Router] --> B[Template Components]
        A --> C[System Filtration]
        A --> D[Content Queries]

        B --> E[Insights Template]
        B --> F[Events Template]
        B --> G[Map Template]
        B --> H[Other Templates]

        C --> I[Multi-select Dropdown]
        C --> J[Multi-level Dropdown]
        C --> K[Tag Display]

        D --> L[GraphQL Queries]
        D --> M[CSV Data Processing]

        E --> N[Card Components]
        F --> N
        G --> O[Map Components]
        H --> N

        I --> P[Redux Filter State]
        J --> P
        K --> P

        P --> Q[Filter Logic Engine]
        Q --> R[Content Filtering]
        R --> S[Analytics Tracking]
    end

    style A fill:#1976d2,color:#fff
    style C fill:#388e3c,color:#fff
    style P fill:#f57c00,color:#fff
    style S fill:#d32f2f,color:#fff
```

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Filter UI
    participant R as Redux Store
    participant F as Filter Engine
    participant C as Content API
    participant A as Analytics

    U->>UI: Select Filter Option
    UI->>R: Dispatch Filter Action
    R->>F: Trigger Filter Processing
    F->>C: Request Filtered Content
    C->>F: Return Filtered Results
    F->>R: Update Content State
    R->>UI: Re-render Components
    UI->>A: Track Filter Event
    A->>A: Send to GA4
    UI->>U: Display Updated Results
```

## Misc. Info

### Browser Compatibility

- **Chrome**: 90+ (Full support)
- **Firefox**: 88+ (Full support)
- **Safari**: 14+ (Full support)
- **Edge**: 90+ (Full support)
- **Mobile Safari**: 14+ (Responsive support)
- **Chrome Mobile**: 90+ (Responsive support)

### Performance Benchmarks

- **Initial Load**: <3 seconds (3G connection)
- **Filter Operation**: <500ms (complex filters)
- **Content Rendering**: <200ms (up to 100 items)
- **Memory Usage**: <50MB (typical usage)
- **Bundle Size**: ~2.5MB (gzipped)

### Internationalization Support

- **Supported Locales**: en-CA, en-US, en-UK, en-AU
- **Filter Labels**: Contentful-managed translations
- **Date Formats**: Locale-specific formatting
- **Number Formats**: Regional number formatting
- **RTL Support**: Not currently implemented

### Content Guidelines

1. **Tag Naming**: Use descriptive, consistent naming conventions
2. **Image Requirements**: Minimum 400x300px for thumbnails
3. **Content Length**: Optimal excerpt length 150-200 characters
4. **SEO Optimization**: Include relevant keywords in titles and descriptions
5. **Accessibility**: Provide alt text for all images

## APIs

### Contentful GraphQL API

#### SystemAfs Query

```graphql
query SystemAfs($id: String!, $locale: String!) {
  systemAfs(id: $id, locale: $locale) {
    template
    afsRichtext
    dataFile {
      url
    }
    afsFiltersCollection {
      items {
        heading
        template
        afsTagNames
        afsTagIds
        isMultiLevel
        isLightMode
      }
    }
    contentfulMetadata {
      tags {
        id
        name
      }
    }
  }
}
```

#### Page Collection Query

```graphql
query PageCollection($tags: [String!], $locale: String!) {
  pageCollection(
    where: { contentfulMetadata: { tags: { id_contains_all: $tags } } }
    locale: $locale
  ) {
    items {
      title
      slug
      template
      publishDate
      afsCardTitle
      afsDescription
      pageThumbnail {
        url
      }
      contentfulMetadata {
        tags {
          id
          name
        }
      }
    }
  }
}
```

### Google Analytics 4 API

#### Filter Event Tracking

```javascript
// AFS Menu Selection Event
{
  event: 'afs_menu',
  category: 'Content Type',
  selected_option: 'Blog Post',
  timestamp: '2024-01-15T10:30:00Z'
}

// Filter Clear Event
{
  event: 'afs_clear_all_filter',
  filter_type: 'insights',
  previous_filter_count: 3,
  timestamp: '2024-01-15T10:31:00Z'
}

// Copy Filter URL Event
{
  event: 'afs_copy_filter_url',
  filter_combination: 'type:blog,author:john-doe',
  page_url: '/insights?type=blog&author=john-doe',
  timestamp: '2024-01-15T10:32:00Z'
}
```

### Google Maps API Integration

#### Map Configuration

```javascript
const mapConfig = {
  apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  libraries: ['places', 'geometry'],
  region: 'CA',
  language: 'en'
}

// Map Component Props
{
  center: { lat: 43.6532, lng: -79.3832 }, // Toronto
  zoom: 10,
  mapTypeId: 'roadmap',
  gestureHandling: 'cooperative',
  zoomControl: true,
  streetViewControl: false,
  fullscreenControl: true
}
```

## Credentials

### Environment Variables Required

#### Production Environment

```bash
# Contentful Configuration
CONTENTFUL_SPACE_ID=your_production_space_id
CONTENTFUL_ACCESS_TOKEN=your_production_access_token
CONTENTFUL_PREVIEW_ACCESS_TOKEN=your_preview_token
NEXT_PUBLIC_PREVIEW=false

# Google Services
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_maps_api_key
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
GA4_API_SECRET=your_ga4_api_secret

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_SECRET=your_firebase_server_key

# Application Configuration
NEXT_PUBLIC_DOMAIN=altusgroup.com
NEXT_PUBLIC_LOG=false
```

#### Development Environment

```bash
# Contentful Configuration (Staging)
CONTENTFUL_SPACE_ID=your_staging_space_id
CONTENTFUL_ACCESS_TOKEN=your_staging_access_token
CONTENTFUL_PREVIEW_ACCESS_TOKEN=your_preview_token
NEXT_PUBLIC_PREVIEW=true

# Development Overrides
NEXT_PUBLIC_LOG=true
NODE_ENV=development
```

### API Key Management

1. **Contentful Keys**: Managed through Contentful dashboard
2. **Google Maps API**: Restricted by domain and IP
3. **GA4 Credentials**: Managed through Google Analytics dashboard
4. **Firebase Keys**: Configured through Firebase console
5. **Vercel Secrets**: Stored securely in Vercel dashboard

### Security Best Practices

1. **Key Rotation**: Quarterly rotation of API keys
2. **Access Restrictions**: Domain and IP-based restrictions
3. **Environment Separation**: Separate keys for dev/staging/production
4. **Monitoring**: API usage monitoring and alerting
5. **Backup Keys**: Secondary keys for emergency access

---

_This documentation is maintained by the Altus Group development team. For updates or questions, please contact the development team or create an issue in the GitHub repository._
