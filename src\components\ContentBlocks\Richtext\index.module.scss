//Default SCSS variables are prepended in next.config.js

// .richText > * {
//   color: inherit;
//   font-size: inherit;
//   line-height: inherit;
//   font-family: inherit;
// }

//Richtext table styles
@mixin td($color) {
  text-align: center;
  font-size: $fs3;
  font-family: $fSansReg;
  color: $color;
  padding: 12px;
  border: 1px solid var(--stroke-gray-4, #c5cdcf);
  }

.richText {
  width: auto;

  span {
    // width: 100%;
    text-wrap: wrap;
    }

  a {
    word-wrap: break-word;
    // display: block;
    max-width: 100%;
    width: auto;
    justify-content: start;
    }

  .link {
    color: $cp2;
    text-decoration: none;
    max-width: 100%;

    //on hover
    &:hover {
      text-decoration: underline;
      }
    }

  .genericLink {
    color: $cp2;
    padding-inline: 2px;
    // text-wrap: nowrap; // disabling because it force pushes link into new line

    &:hover {
      color: $cs2;
      background-color: $cp2;
      text-decoration: none;
      transition: color 0.25s,
      background-color 0.25s;
      }
    }

  table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--stroke-gray-4, #c5cdcf);
    background: var(--White, $cs2);
    }

  tr {
    th {
      background: var(--primary-altus-primary, $cp1);
      @include td(var(--White, $cn8));
      font-family: $fSansBld;
      }
    }

  th {
    td {
      padding: 11px;
      }
    }

  td {
    @include td(var(--text-text-primary, $cn2));
    }

  tr:nth-child(even) {
    background-color: $cn8;
    }

  tr:nth-child(odd) {
    background-color: $cn7;
    }

  @media screen and (max-width: $smScreenSize) {
    table {
      table-layout: auto;

      thead,
      td {
        min-width: 150px;
        }
      }
    }
  }

// dark theme for links, if dark mode is true then links will be of yellow colour
.darkTheme {
  .link {
    color: $cs1;
    text-decoration: none;

    //on hover
    &:hover {
      text-decoration: underline;
      }
    }

  .genericLink {
    color: $cs1;

    &:hover {
      color: $cp1;
      background-color: $cs1;
      text-decoration: underline;
      text-decoration: none;
      transition: color 0.25s,
      background-color 0.25s;
      }
    }
  }