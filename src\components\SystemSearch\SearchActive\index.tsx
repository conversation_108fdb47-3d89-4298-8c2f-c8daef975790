'use client'
// Algolia recommendation and search imports
import recommend from '@algolia/recommend'
import { useEffect, useState } from 'react'
import { useInstantSearch } from 'react-instantsearch'
import { useSelector } from 'react-redux'
// Custom hooks and utilities
import { useWindowSize } from '../../../hooks/useWindowSize'
import { setRecentSearchReducer } from '../../../redux/slices/searchSlice'
import { useAppDispatch } from '../../../redux/store'
import { buttonClickEvent } from '../../../utils/analyticsEvents'
// Component imports
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import CardGenericSearch from '../../Cards/CardGeneric/CardGenericSearch'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../Kernel'
import MenuListMegamenu from '../../Navs/MenuList/MenuListMegamenu'
// Search utilities and configuration
import { getCardsData, getHref } from '../searchUtil'
import { SearchActiveD } from './defaults'
import {
  exludeInsightsAndEventsFilter,
  filterRecommendations,
} from './filterRecommendation'
import styles from './index.module.scss'
import { SearchActiveI } from './interface'

/**
 * SearchActive Component
 *
 * Displays search results and related content when user has entered a search query.
 * Features include:
 * - Real-time search results from Algolia
 * - Related content recommendations
 * - Filtered results (excluding insights and events for certain domains)
 * - Analytics tracking for search interactions
 * - Responsive layout with different views for mobile/desktop
 */

// Initialize Algolia recommendation client for related content suggestions
// Uses same credentials as search client but provides recommendation-specific functionality
export const recommendClient = recommend(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)

// Algolia index name for recommendations
// Same index used for both search and recommendations
export const recommendIndexName = process.env
  .NEXT_PUBLIC_ALGOLIA_INDEX as string

// Domains that have limited search functionality
// These domains exclude certain content types from search results
export const excludedDomains = [
  'verifino.com',
  'financeactive.com',
  'one11advisors.com',
]

export default function SearchActive(props: SearchActiveI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  // Get search results from Algolia InstantSearch context
  // This hook provides access to current search state and results
  const { results } = useInstantSearch()

  // State for storing categorized search results
  // These are used to display different types of content separately
  const [eventsCards, setEventsCards] = useState([])
  const [insightsCards, setInsightsCards] = useState([])

  // Transform Algolia search hits into navigation links
  // Filters out insights and events content, then maps to link format
  const linksFromHits = results.hits
    .filter(exludeInsightsAndEventsFilter) // Remove insights/events from main results
    .map((hit) => ({
      objectId: hit.objectID,           // Algolia object identifier
      textContent: hit.shortTitle,      // Display text for the link
      href: getHref(hit.slug),         // Generate proper URL from slug
    }))

  // Get current domain for domain-specific functionality
  // Used to determine which features are available on current site
  let currentDomain = window.location.hostname
  let recommendationsArray = []

  // Remove 'www.' prefix for consistent domain matching
  if (currentDomain.startsWith('www.')) {
    currentDomain = currentDomain.slice(4)
  }

  // if (!excludedDomains.includes(currentDomain)) {
  //   const { recommendations } = useRelatedProducts({
  //     recommendClient,
  //     indexName: recommendIndexName,
  //     objectIDs: results.hits.map((hit) => hit.objectID),
  //   })

  //   recommendationsArray = recommendations
  // }

  const updatedProps: SearchActiveI = {
    ...SearchActiveD,
    ...props,
    menuList: {
      ...SearchActiveD.menuList,
      links: linksFromHits,
    },
  }

  // l(insightsAndResearchCardD);

  const { size } = useWindowSize()

  const buttonHeading = `${updatedProps.isLightMode ? 'cn2' : 'cs2'}`
  const classHeading = `${updatedProps.isLightMode ? 'cp2' : 'cs1'}`

  const { rePopulate } = useSelector((state: any) => state.search)
  const dispatch = useAppDispatch()

  useEffect(() => {
    const links = updatedProps?.menuList?.links

    if (links && links?.length > 0 && rePopulate) {
      const inputValue = {
        objectId: links[0]?.objectId as string,
        textContent: links[0]?.textContent as string,
        href: links[0]?.href as string,
      }
      dispatch(setRecentSearchReducer(inputValue))
    }
  }, [results])

  useEffect(() => {
    const { eventsAndWebinars, insightsAndResearch } = filterRecommendations(
      results.hits
    )

    setEventsCards(getCardsData(eventsAndWebinars))
    const insightsAndResearchCardD = getCardsData(insightsAndResearch)
    setInsightsCards(insightsAndResearchCardD)
  }, [results.hits])

  //Returning the Input enclosed within Kernel
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: styles.searchResultContainer,
      }}
    >
      {results.hits.length === 0 ? (
        <>
          <div className={styles.button}>
            <p>
              Sorry we couldn&apos;t find any results. Can&apos;t find what
              you&apos;re looking for?{' '}
            </p>
            <SimpleButtonWIcon
              {...updatedProps.button}
              isLightMode={false}
              href={'/contact-us'}
              htmlAttr={{ className: styles.cta, onClick: () => { buttonClickEvent("Contact us", "/contact-us") } }}
              isIconPrefixed={false}
              isChevron2Arrow={true}
            />
          </div>
        </>
      ) : (
        <>
          <div className={styles.left}>
            {linksFromHits?.length > 0 && (
              <MenuListMegamenu
                {...updatedProps?.menuList}
                htmlAttr={{ className: styles.menuList }}
                isLightMode={updatedProps.isLightMode}
              />
            )}
            {size === 'large' && (
              <div className={styles.button}>
                <SimpleParagraph
                  {...updatedProps.ButtonHeading}
                  htmlAttr={{ className: buttonHeading }}
                />
                <SimpleButtonWIcon
                  {...updatedProps.button}
                  isLightMode={false}
                  href={'/contact-us'}
                  htmlAttr={{ className: styles.cta, onClick: () => { buttonClickEvent("Contact us", "/contact-us") } }}
                  isIconPrefixed={false}
                  isChevron2Arrow={true}
                />
              </div>
            )}
          </div>
          <div className={styles.cardsContainer}>
            {insightsCards?.length > 0 && (
              <div className={`${styles.cards}`}>
                <SimpleParagraph
                  {...updatedProps.CardListHeading1}
                  htmlAttr={{ className: classHeading }}
                />
                {insightsCards?.map((card, ind) => (
                  <CardGenericSearch
                    key={ind}
                    {...card}
                    isLightMode={updatedProps.isLightMode}
                    htmlAttr={{ className: styles.SingleCard }}
                  />
                ))}
              </div>
            )}
            {eventsCards.length > 0 && (
              <div className={`${styles.cards}`}>
                <SimpleParagraph
                  {...updatedProps.CardListHeading2}
                  htmlAttr={{ className: classHeading }}
                />
                {eventsCards?.map((card, ind) => (
                  <CardGenericSearch
                    key={ind}
                    {...card}
                    isLightMode={updatedProps.isLightMode}
                    htmlAttr={{ className: styles.SingleCard }}
                  />
                ))}
              </div>
            )}
          </div>
          {(size === 'mid' || size === 'small') && (
            <div className={styles.button}>
              <SimpleParagraph
                {...updatedProps.ButtonHeading}
                htmlAttr={{ className: buttonHeading }}
              />
              <SimpleButtonWIcon
                {...updatedProps.button}
                isLightMode={false}
                href={'/contact-us'}
                htmlAttr={{ className: styles.cta, onClick: () => { buttonClickEvent("Contact us", "/contact-us") } }}
                isIconPrefixed={false}
                isChevron2Arrow={true}
              />
            </div>
          )}
        </>
      )}
    </Kernel>
  )
}
