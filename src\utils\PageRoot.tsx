import { ErrorBoundary } from 'react-error-boundary'
import { BUISNESS_DOMAINS } from '.'
import Popup from '../components/Containers/Popup'
import { default as KernelPopup } from '../components/Kernel/@core/Popup'
import TheError from '../components/Kernel/@core/TheError/TheError'
import NotificationsWithAllComps from '../components/Notifications'
import { getDomainShortName, LOCALE_CONSTANT } from '../globals/utils'
import { fetchGraphQL } from '../lib/api'
import NavigationFooterRouter from '../lib/componentsRouter/NavigationFooterRouter'
import NavigationHeaderRouter from '../lib/componentsRouter/NavigationHeaderRouter'
import PageRouter from '../lib/componentsRouter/PageRouter'
import { mapDataToComponent } from '../lib/propsMapping/index.mapping'
import { getConfigurationsCollectionQuery } from '../lib/queries/globalConfig.query'
import ScrollIdProvider from '../providers/ScrollIdProvider'
import FlushPageFloatingButton from './FlushPageFloatingButton'
import {
  fetchAndFilterNotifications,
  fetchComponentData,
  fetchLocalizationData,
  fetchNavigationFooterData,
  fetchNavigationHeaderData,
  fetchPageDataFromSlug,
} from './pageUtils'

/** Dynamic Page Root Component
 * This component is responsible for rendering a dynamic page based on the provided slug.
 * It fetches the necessary data, including page content, notifications, and navigation components.
 * It also handles localization and sorting of components based on configuration.
 * * @param {Object} props - The properties passed to the component.
 * @param {Object} props.params - The route parameters containing the slug.
 * @param {string} props.pageId - The ID of the page to be rendered.
 * @param {Object} props.sortingConfig - Configuration for sorting components.
 * @returns {JSX.Element} The rendered page component with all necessary data and components.
 * @throws {Error} If there is an issue fetching the page data or notifications.
 **/

async function PageRoot({ params, pageId, sortingConfig = {} }) {
  let notificationData = null
  let pageData = null
  let navigationData = {}
  let footerData = {}
  let anotherLangLocalization = null

  let locale = LOCALE_CONSTANT[params.slug?.[0]] || 'en-CA'

  const domain = process.env.NEXT_PUBLIC_DOMAIN || ''

  if (BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN) {
    if (params?.slug?.[params?.slug?.length - 1] === 'fr') {
      params.slug?.pop()
      locale = LOCALE_CONSTANT['fr']
    }
  }

  const fullUrl = params.slug?.join('/') || '/'

  navigationData = await fetchNavigationHeaderData(locale)
  footerData = await fetchNavigationFooterData(locale)

  const fetchData = async (domainCode: string) => {
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items
    )

    const matchedData = res?.find((item: any) => {
      return (
        item?.scope === getDomainShortName(domainCode)?.toUpperCase() &&
        item?.type === 'Locale'
      )
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return

    const data = JSON.parse(matchedData)

    return data
  }

  const domainData = await fetchData(domain)

  const allowedLanguages = domainData?.allowedLanguages || ['en-CA']

  /**
   * Fetch page data based on the provided slug and locale.
   * This function retrieves the page data from the GraphQL API using the slug and locale.
   * It also handles sorting of components based on the provided sorting configuration.
   * @param {string} fullUrl - The full URL slug of the page.
   * @param {string} locale - The locale for which the page data is requested.
   * @param {string} pageId - The ID of the page to be fetched.
   */
  const pageResponse = await fetchPageDataFromSlug(fullUrl, locale, pageId)
  pageData = pageResponse?.data?.pageCollection?.items?.at(0)
  const config = pageData?.configurations?.dynamicpage

  let sortedComponentIds: string[] | null = null

  for (const [key, value] of Object.entries(sortingConfig)) {
    const ids = config?.[key]?.[value]
    if (ids && Array.isArray(ids)) {
      sortedComponentIds = ids
      break // use the first matching sorting key
    }
  }
  anotherLangLocalization = await fetchLocalizationData(pageData?.sys?.id)

  const allCompDataArray = await fetchComponentData(
    pageData?.contentCollection.items,
    locale,
    fullUrl
  )

  /**
   * Sort components based on the provided sortedComponentIds.
   * If sortedComponentIds is not provided, it returns all components in their original order.
   * If sortedComponentIds is provided, it sorts the components according to the order defined in
   * sortedComponentIds, followed by any remaining components that are not in sortedComponentIds.
   * @returns {Array} An array of components sorted according to sortedComponentIds.
   * @description
   * This function ensures that components are displayed in a specific order defined by the
   * sortedComponentIds array. If a component's ID is not found in sortedComponentIds
   */
  const sortedComponents = (() => {
    if (!sortedComponentIds?.length) return allCompDataArray

    const sortedSet = new Set(sortedComponentIds)

    // Components in the order defined by sortedComponentIds
    const sorted = sortedComponentIds
      .map((id) => allCompDataArray.find((c) => c?.sys?.id === id))
      .filter(Boolean)

    // Remaining components not in sortedComponentIds
    const remaining = allCompDataArray.filter((c) => !sortedSet.has(c?.sys?.id))

    return [...sorted, ...remaining]
  })()

  notificationData = await fetchAndFilterNotifications(fullUrl, locale)
  const NotificationComponents = notificationData
    ? notificationData.map((item, index) => (
        <div key={index}>{mapDataToComponent(item)}</div>
      ))
    : null

  let defaultLangLocalization = {
    en: { slug: null },
    fr: { slug: '?lang=fr' },
    de: { slug: '?lang=de' },
    es: { slug: '?lang=es' },
    it: { slug: '?lang=it' },
    nl: { slug: '?lang=nl' },
  }

  navigationData = { ...navigationData, locale, languages: allowedLanguages }

  const PageJSX = () => {
    return (
      <ScrollIdProvider
        response={pageResponse}
        allCompDataArray={sortedComponents}
      >
        {process.env.VERCEL_ENV !== 'production' && <FlushPageFloatingButton />}
        {NotificationComponents}
        <ErrorBoundary FallbackComponent={TheError}>
          {!pageData?.isHeaderNavigationHidden && (
            <NavigationHeaderRouter
              {...navigationData}
              isTranslucent={pageData?.isTranslucent}
              isNavLightMode={pageData?.isNavLightMode}
              isLightBgImage={pageData?.isLightBgImage}
              langLocalization={
                BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN
                  ? defaultLangLocalization
                  : anotherLangLocalization
              }
            />
          )}
          <NotificationsWithAllComps />
        </ErrorBoundary>
        <ErrorBoundary FallbackComponent={TheError}>
          <PageRouter componentData={sortedComponents} pageData={pageData} />
        </ErrorBoundary>
        <ErrorBoundary FallbackComponent={TheError}>
          {!pageData?.isFooterNavigationHidden && (
            <NavigationFooterRouter locale={locale} {...footerData} />
          )}
        </ErrorBoundary>
        <ErrorBoundary FallbackComponent={TheError}>
          <Popup isMaxedOut={false} />
          <KernelPopup isOpen={false} />
        </ErrorBoundary>
      </ScrollIdProvider>
    )
  }
  return <PageJSX />
}

export default PageRoot
