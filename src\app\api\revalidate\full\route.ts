/**
 * Full Site Revalidation API Route
 *
 * This endpoint provides a secure way to revalidate the entire site's cache,
 * including all pages and layouts. It uses Next.js 13+ App Router revalidation
 * with layout-level cache invalidation for comprehensive cache clearing.
 *
 * Security:
 * - Requires a secret key parameter for authentication
 * - Only accepts GET requests with the correct key
 * - Returns 401-equivalent response for invalid keys
 *
 * Usage:
 * GET /api/revalidate/full?key=sf9ds8fd9fgdsf8dgk
 *
 * Cache Strategy:
 * - Uses revalidatePath('/', 'layout') to invalidate all cached content
 * - Layout-level revalidation ensures complete cache flush
 * - Affects all pages, components, and data fetching
 *
 * Use Cases:
 * - Emergency cache clearing after critical content updates
 * - Deployment-triggered cache invalidation
 * - Manual cache flush for troubleshooting
 */

import { revalidatePath } from 'next/cache'
import { NextRequest } from 'next/server'

/**
 * Handle GET request for full site revalidation
 * Validates secret key and triggers complete cache invalidation
 * @param request - Next.js request object containing query parameters
 * @returns JSON response with revalidation status and timestamp
 */
export async function GET(request: NextRequest) {
  // Extract authentication key from query parameters
  const key = request.nextUrl.searchParams.get('key')

  // Validate secret key for security
  if (key === 'sf9ds8fd9fgdsf8dgk') {
    // Revalidate entire site by invalidating root layout cache
    // This cascades to all pages and components under the layout
    revalidatePath('/', 'layout')

    return Response.json({
      revalidated: true,
      now: Date.now(),
      message: 'All paths revalidated.',
    })
  } else {
    // Return error response for invalid or missing key
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Invalid key.',
    })
  }
}
