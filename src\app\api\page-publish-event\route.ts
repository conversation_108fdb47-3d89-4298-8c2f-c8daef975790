import { NextRequest } from 'next/server'


export async function POST(request: NextRequest) {
    const req = await request.json()
    const pageFields = req.fields

    if (req.sys.revision !== 1) {
        return Response.json({
            success: false,
            timestamp: Date.now(),
            message: 'Not the first revision.',
        })
    }

    if (pageFields?.slug?.['en-CA'] && pageFields?.publishDate?.['en-CA']) {
        const publishDate = pageFields?.publishDate?.['en-CA']?.split('T')?.[0]

        const eventObj = {
            slug: pageFields?.slug?.['en-CA'],
            internal_name: pageFields?.internalName?.['en-CA'],
            publish_date: publishDate
        }

        try {
            /**
             * GA4 Measurement Protocol Implementation
             *
             * Sends server-side events directly to GA4 using the Measurement Protocol.
             * This bypasses client-side tracking and ensures events are captured even
             * when users have ad blockers or JavaScript disabled.
             *
             * API Endpoint: https://www.google-analytics.com/mp/collect
             * Required Parameters:
             * - api_secret: Server-side API secret from GA4 property
             * - measurement_id: GA4 property measurement ID (G-XXXXXXXXXX)
             *
             * Event Structure:
             * - client_id: Unique identifier for the event source
             * - events: Array of event objects with name and parameters
             */
            const response = await fetch(
                `https://www.google-analytics.com/mp/collect?api_secret=${process.env.GA4_API_SECRET}&measurement_id=${process.env.NEXT_PUBLIC_GA4_MEASUREMENT_ID}`,
                {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        client_id: "contentful-webhook", // Static client ID for Contentful webhook events
                        events: [
                            {
                                name: "page_publish", // Custom event name for content publishing
                                params: eventObj, // Event parameters containing page details
                            },
                        ],
                    }),
                }
            );

            // Handle GA4 API response
            if (response.ok) {
                console.log("GA4 event sent successfully.");
                Response.json({ success: true });
            } else {
                console.error("Failed to send GA4 event.");
                Response.json({ success: false, message: "Failed to send GA4 event." }, { status: 500 });
            }
        } catch (error: any) {
            console.error("Error sending GA4 event:", error);
            Response.json({ success: false, error: error.message }, { status: 500 });
        }
        return Response.json({
            success: true,
            timestamp: Date.now(),
            message: 'GA4 page_publish event sent.',
            data: eventObj
        })
    } else {
        return Response.json({
            success: false,
            timestamp: Date.now(),
            message: 'Page slug or publishDate is missing.',
        })
    }

}
