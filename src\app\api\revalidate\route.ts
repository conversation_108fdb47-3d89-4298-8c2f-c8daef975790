/**
 * Tag-Based Cache Revalidation API Route
 *
 * This endpoint provides granular cache invalidation using Next.js cache tags.
 * It allows selective revalidation of specific content types or data sources
 * without affecting the entire site cache, providing efficient cache management.
 *
 * Cache Tag Strategy:
 * - Tags are assigned to fetch requests in fetchGraphQL functions
 * - Common tags: 'pages', 'components', 'navigation', 'global-config'
 * - Content-specific tags: 'insights', 'case-studies', 'resources'
 * - Domain-specific tags: 'altus', 'reonomy', etc.
 *
 * Usage:
 * POST /api/revalidate
 * Body: { "tag": "pages" }
 *
 * Benefits:
 * - Selective cache invalidation reduces unnecessary recomputation
 * - Faster revalidation compared to full site cache clearing
 * - Maintains performance while ensuring content freshness
 * - Supports automated content management workflows
 */

import { revalidateTag } from 'next/cache'
import { NextRequest } from 'next/server'

/**
 * Handle POST request for tag-based cache revalidation
 * Invalidates cache entries associated with the specified tag
 * @param request - Next.js request object containing JSON body with tag
 * @returns JSON response with revalidation status, timestamp, and tag
 */
export async function POST(request: NextRequest) {
  const req = await request.json()
  const tag = req?.tag

  if (tag) {
    // Log revalidation activity for debugging and monitoring
    console.log('POST: revalidate tag', tag)

    // Invalidate all cache entries associated with the specified tag
    // This affects all fetch requests that were tagged with this identifier
    revalidateTag(tag)

    return Response.json({ revalidated: true, now: Date.now(), tag })
  }

  // Return error response when tag is missing
  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing tag to revalidate',
  })
}
