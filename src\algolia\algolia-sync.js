/**
 * Algolia Index Synchronization Script
 *
 * This script handles the synchronization of content from Contentful CMS to Algolia search index.
 * It performs the following operations:
 * - Fetches page data from Contentful using GraphQL queries
 * - Transforms the data into Algolia-compatible records
 * - Clears existing index and uploads new records
 * - Handles both regular pages and experimental pages
 * - Runs only in production or when explicitly allowed
 *
 * Environment Variables Required:
 * - NEXT_PUBLIC_ALGOLIA_INDEX: Target Algolia index name
 * - NEXT_PUBLIC_ALGOLIA_APPLICATION_ID: Algolia application ID
 * - ALGOLIA_ADMIN_KEY: Admin API key for write operations
 * - VERCEL_ENV: Environment indicator (production check)
 * - NEXT_PUBLIC_ALGOLIA_SYNC_ALLOWDED: Override for non-production sync
 */

// Algolia search client import (lite version for better performance)
import algoliasearch from 'algoliasearch/lite.js'
// Environment configuration
import dotenv from 'dotenv'
// Contentful integration helpers
import {
  fetchGraphQL,
  getConfigurationByScopeAndType,
  getDomainShortName,
} from '../../confighelper.js'
// GraphQL query helpers for fetching page data
import {
  getPageForAlgoia,
  getPageForAlgoliaofExperimentation,
} from '../../queryhelper.js'

// Load environment variables based on environment
if (process.env.NODE_ENV !== 'production') {
  dotenv.config({ path: '.env.local' })
} else {
  dotenv.config({ path: '.env' })
}

// Algolia index name from environment variables
const algoliaIndex = process.env.NEXT_PUBLIC_ALGOLIA_INDEX

/**
 * Fetch regular page data from Contentful for Algolia indexing
 * Uses GraphQL query to retrieve all pages with necessary fields for search
 * @returns {Promise<Object>} Contentful page collection data
 */
const getPageData = async () => {
  const data = await fetchGraphQL(getPageForAlgoia())
  return data
}

const getPageDataForExperimentation = async () => {
  const isPreview = process.env.NEXT_PUBLIC_PREVIEW === 'true'
  const resD = await getConfigurationByScopeAndType(
    'Experimentation',
    `${getDomainShortName(process.env.NEXT_PUBLIC_DOMAIN)?.toUpperCase()}`
  )
  const pagesObject = {}
  const pagesToExperiment = resD?.data?.map((expPage) => {
    if (
      expPage?.masterPage &&
      expPage?.experimentationId &&
      !expPage?.isDeleted &&
      (isPreview || expPage?.isPublished) &&
      expPage?.experimentationPages &&
      expPage?.experimentationPages?.length
    ) {
      pagesObject[expPage?.experimentationPages?.[0]?.id] = expPage?.masterPage
      return expPage?.experimentationPages?.[0]?.id
    }
  })

  const data = await fetchGraphQL(
    getPageForAlgoliaofExperimentation([...new Set(pagesToExperiment)])
  )
  return { data, pagesObject }
}

/**
 * Transform Contentful page data into Algolia-compatible records
 * Converts Contentful's sys.id to Algolia's required objectID field
 * @param {Object} response - Contentful GraphQL response
 * @returns {Array} Array of Algolia records with objectID
 */
const transformToAlgoliaRecords = (response) => {
  return response?.data?.pageCollection?.items
    ?.map(({ sys, ...rest }) =>
      sys?.id ? { objectID: sys.id, ...rest } : null // Use Contentful ID as Algolia objectID
    )
    ?.filter(Boolean) // Remove null entries
}

/**
 * Transform experimental page data with additional slug information
 * Similar to regular transformation but includes slug mapping for experimental pages
 * @param {Object} response - Contentful GraphQL response
 * @param {Object} pagesObject - Mapping of page IDs to slugs
 * @returns {Array} Array of Algolia records with objectID and slug
 */
const transformToAlgoliaRecordsForExperiment = (response, pagesObject) => {
  return response?.data?.pageCollection?.items
    ?.map(({ sys, ...rest }) =>
      sys?.id ? { objectID: sys.id, ...rest, slug: pagesObject[sys?.id] } : null
    )
    ?.filter(Boolean) // Remove null entries
}

/**
 * Main function to synchronize Contentful data to Algolia index
 * Performs complete index refresh by clearing existing data and uploading new records
 * Combines regular pages and experimental pages into a single index
 *
 * Security: Only runs in production environment or when explicitly allowed
 * Process:
 * 1. Environment check for security
 * 2. Initialize Algolia client with admin credentials
 * 3. Fetch data from Contentful (regular + experimental pages)
 * 4. Transform data to Algolia format
 * 5. Clear existing index
 * 6. Upload new records
 *
 * @returns {Promise} Algolia indexing response or error
 */
export const putDataToAlgolia = async () => {
  // Security check: Only allow sync in production or when explicitly enabled
  if (
    process.env.VERCEL_ENV !== 'production' &&
    process.env.NEXT_PUBLIC_ALGOLIA_SYNC_ALLOWDED !== 'true'
  ) {
    console.log(
      'Algolia Script not run as this is not a production environment......'
    )
    return
  } else {
    console.log('Algolia synced')
  }

  // Initialize Algolia client with admin credentials for write operations
  // Note: Uses ALGOLIA_ADMIN_KEY (not search-only key) for index modifications
  const algoliaClient = algoliasearch(
    process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID,
    process.env.ALGOLIA_ADMIN_KEY
  )

  // Get reference to the target Algolia index
  const index = algoliaClient.initIndex(algoliaIndex)

  // Fetch regular page data from Contentful
  const finalResponse = await getPageData()

  // Transform regular pages to Algolia format
  const algoliaRecords = transformToAlgoliaRecords(finalResponse)

  // Fetch experimental page data with slug mapping
  const { data: finalResponseExp, pagesObject } =
    await getPageDataForExperimentation()
  // Transform experimental pages to Algolia format
  const algoliaRecordsExperimentation = transformToAlgoliaRecordsForExperiment(
    finalResponseExp,
    pagesObject
  )

  // Debug logging for record counts
  console.log(
    algoliaRecordsExperimentation,
    algoliaRecordsExperimentation?.length,
    algoliaRecords?.length
  )

  // Combine regular and experimental page records into single array
  // This creates a unified search index with all content types
  const mergedAlgoliaRecords = [
    ...algoliaRecords,
    ...algoliaRecordsExperimentation,
  ]

  // Optional: Remove duplicate pages if needed
  // Currently commented out to allow multiple versions of pages
  // const uniqueAlgoliaRecords = Array.from(
  //   new Map(mergedAlgoliaRecords.map((item) => [item.objectID, item])).values()
  // )

  try {
    // Clear existing index to ensure fresh data
    // This prevents stale content from remaining in search results
    await index.clearObjects()

    // Upload new records to Algolia index
    // saveObjects() handles batch upload efficiently
    return await index
      .saveObjects(mergedAlgoliaRecords)
      .then((res) => {
        console.log('Pages indexing into Algolia')
        return res
      })
      .catch((error) => {
        console.error('Error when indexing page into Algolia', error)
        return error
      })
  } catch (error) {
    console.log(error)
    return error
  }
}

putDataToAlgolia()
