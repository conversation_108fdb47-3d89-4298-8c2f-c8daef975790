/**
 * Page-Specific Cache Revalidation API Route
 *
 * This endpoint provides targeted cache invalidation for specific pages or paths.
 * It supports batch revalidation of multiple pages and includes CORS support
 * for cross-origin requests from content management systems and external tools.
 *
 * Features:
 * - Batch revalidation of multiple page paths
 * - CORS support for external integrations
 * - Path-based cache invalidation using Next.js revalidatePath
 * - Support for both trailing slash and non-trailing slash URLs
 *
 * Usage:
 * POST /api/revalidate/page
 * Body: { "slugs": ["/about", "/contact", "/insights/article-1"] }
 *
 * CORS Configuration:
 * - Supports preflight OPTIONS requests
 * - Validates origins against allowed patterns and explicit origins
 * - Enables integration with Contentful webhooks and other CMS systems
 */

import { revalidatePath } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';
import { corsAllowedOrigins, corsAllowedPattern } from '../../../../globals/utils';

/**
 * Handle CORS preflight requests
 * Validates origin and sets appropriate CORS headers for cross-origin access
 * @param req - Next.js request object containing origin header
 * @returns Response with CORS headers if origin is allowed
 */
export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin') ?? ''
  const response = new NextResponse(null, { status: 200 })

  // Set CORS headers if the origin is allowed
  // Supports both regex patterns and explicit origin lists
  if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
    console.log('Setting CORS headers for origin in OPTIONS: ', origin)
    response.headers.append('Access-Control-Allow-Origin', origin)
  }

  // Configure allowed methods and headers for CORS
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', '*')
  return response
}

/**
 * Handle POST request for page-specific cache revalidation
 * Processes batch revalidation requests for multiple page paths
 * @param request - Next.js request object containing JSON body with slugs array
 * @returns JSON response with revalidation status, timestamp, and processed request
 */
export async function POST(request: NextRequest) {
  const req = await request.json()
  let slugs = req?.slugs

  // Log incoming request for debugging and monitoring
  console.log('POST: req body', req)

  // Process revalidation if slugs are provided
  if (slugs?.length > 0) {
    // Iterate through each slug and invalidate its cache
    // This triggers regeneration on next request for each path
    slugs.forEach((slug: string) => {
      revalidatePath(slug)
    })

    // Return success response with processed request details
    return Response.json({ revalidated: true, now: Date.now(), req })
  }

  // Return error response when no slugs are provided
  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing slug to revalidate',
  })
}
