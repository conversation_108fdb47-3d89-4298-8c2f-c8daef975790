'use client'
import { TheSEOBlock } from '../../../globals/utils'
import SimpleBar from '../SimpleBar'
import styles from './index.module.scss'
import { ProgressBarI } from './interface'
import { getSimpleBarSEObj } from './seo'

export default function ProgressBar(props: ProgressBarI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: ProgressBarI = {
    // ...ProgressBarD,
    ...props,
  }
  const isIncreasingWidth = updatedProps.progressWidth === '100%' // Adjust the condition based on your requirement

  const transitionDuration = updatedProps.duration || 500 // Default duration is 500ms
  let className = `${styles.progressBar} ${updatedProps.progressColour} ${isIncreasingWidth && updatedProps.transition ? styles.transition : ''
    }`

  if (updatedProps.isRounded) {
    className += ' rounded max'
  }

  return (
    <SimpleBar {...updatedProps}>
      <div
        className={className}
        style={{
          width: updatedProps.progressWidth,
          transition: isIncreasingWidth
            ? `width ${transitionDuration}ms ease-in-out`
            : 'none',
        }}
        {...updatedProps.htmlAttr}
      ></div>
      {updatedProps.children}
      <TheSEOBlock seoObj={getSimpleBarSEObj(updatedProps)} />
    </SimpleBar>
  )
}
