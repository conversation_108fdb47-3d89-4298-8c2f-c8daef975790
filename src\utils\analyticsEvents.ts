/**
 * Analytics Events Module
 *
 * This module provides functions for tracking user interactions and events through Google Tag Manager (GTM)
 * and Google Analytics 4 (GA4). All events are pushed to the dataLayer for GTM to process and forward
 * to various analytics platforms including GA4, Microsoft Advertising (UET), and other tracking services.
 *
 * Key Features:
 * - Form interaction tracking (success, error, start, close, abandon)
 * - Search behavior tracking (open, close, input, results)
 * - Advanced Filtering System (AFS) interactions
 * - Button and CTA click tracking
 * - Client-side browser detection for safe execution
 * - GA4 Measurement Protocol integration
 *
 * All functions use the dataLayer pattern for GTM integration and include browser detection
 * to prevent server-side execution errors.
 */

import { getClientSideCookie, isBrowser } from "../globals/utils";

/**
 * Track successful form submissions
 * Sends custom_form_success event to GTM dataLayer with form details
 * Used for conversion tracking and form performance analysis
 *
 * @param contentfulId - Unique identifier for the form from Contentful CMS
 * @param formCategory - Category/type of form (e.g., 'contact', 'newsletter', 'demo')
 * @param contactInquiryType - Optional: Type of contact inquiry for lead qualification
 * @param productInterest - Optional: Product the user is interested in
 */
export function formSuccessEvent({
    contentfulId,
    formCategory,
    contactInquiryType = null,
    productInterest = null,
}) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'custom_form_success',
            formId: contentfulId,
            formCategory,
            contactInquiryType,
            productInterest
        })
    }
}

/**
 * Track form submission errors
 * Sends custom_form_error event to GTM dataLayer for error analysis
 * Helps identify problematic forms and improve user experience
 *
 * @param contentfulId - Unique identifier for the form from Contentful CMS
 * @param formCategory - Category/type of form that encountered the error
 */
export function formErrorEvent({
    contentfulId,
    formCategory
}) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'custom_form_error',
            formId: contentfulId,
            formCategory
        })
    }
}

/**
 * Search Analytics Functions
 *
 * These functions track user search behavior and interactions with the Algolia-powered
 * search system. They provide insights into search usage patterns, popular queries,
 * and search result effectiveness.
 */

/**
 * Track when user opens the search interface
 * Triggered when search overlay/modal is displayed
 * Helps measure search feature adoption and usage frequency
 */
export function searchOpenEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'open_search_screen',
        })
    }
}

/**
 * Track when user closes the search interface
 * Triggered when search overlay/modal is dismissed
 * Used to calculate search session duration and abandonment rates
 */
export function searchCloseEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'close_search_screen',
        })
    }
}

/**
 * Track when user starts typing in search input
 * Triggered on first character entry in search field
 * Measures search engagement and intent to search
 * Note: Only fires once per search session to avoid spam
 */
export function enteredSearchInputEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'entered_search_input',
        })
    }
}

/**
 * Track clicks on search results
 * Measures search result relevance and user satisfaction
 * Helps optimize search ranking and content discoverability
 *
 * @param href - Optional URL of the clicked search result for detailed analysis
 */
export function searchResultClickEvent(href?: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'clicked_search_result',
            clicked_url: href
        })
    }
}

/**
 * Advanced Filtering System (AFS) Analytics Functions
 *
 * These functions track user interactions with the Advanced Filtering System,
 * providing insights into how users discover and filter content. AFS is used
 * across various content types including insights, case studies, and resources.
 */

/**
 * Track when users copy filtered URL for sharing
 * Measures content sharing behavior and filter utility
 * Helps identify popular filter combinations worth promoting
 *
 * @param copiedUrl - The complete filtered URL that was copied to clipboard
 */
export function copyFilterEvent(copiedUrl: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_copy_filter_url',
            copied_url: copiedUrl
        })
    }
}

/**
 * Track when users clear all applied filters
 * Indicates filter complexity or user desire to start fresh
 * Helps optimize filter UX and default states
 */
export function clearAllFilterEvent() {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_clear_all_filter',
        })
    }
}

/**
 * Track sorting option selections
 * Measures user preferences for content organization
 * Helps determine optimal default sorting and available options
 *
 * @param option - The selected sorting option (e.g., 'date', 'relevance', 'title')
 */
export function sortByEvent(option: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_sort_by',
            selected_option: option
        })
    }
}

/**
 * Track filter menu interactions
 * Captures detailed filter usage patterns by category and selection
 * Provides granular insights into content discovery behavior
 *
 * @param category - The filter category (e.g., 'industry', 'content-type', 'region')
 * @param option - The specific option selected within the category
 */
export function afsMenuEvent(category: string, option: string) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'afs_menu',
            category: category,
            selected_option: option
        })
    }
}

/**
 * Button and CTA (Call-to-Action) Analytics Functions
 *
 * These functions track user interactions with buttons and CTAs throughout the site,
 * providing insights into user engagement patterns and conversion paths.
 */

/**
 * Track generic button clicks
 * Simple tracking for standard button interactions
 * Used for basic click tracking without intent classification
 *
 * @param btnText - The text content of the clicked button
 * @param url - The URL the button navigates to (if applicable)
 */
export function buttonClickEvent(btnText, url) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'button_click',
            button_text: btnText,
            clicked_url: url
        })
    }
}

/**
 * CTA Intent Classification System
 *
 * Categorizes CTAs by their intended action to provide better analytics insights:
 * - OPEN_POPUP: CTAs that open modals, overlays, or popup windows
 * - GET_INFO: CTAs for contact forms, phone calls, email, or information requests
 * - GO_TO: Navigation CTAs that redirect to other pages or sections
 * - UNCLASSIFIED: CTAs that don't fit other categories or need manual classification
 */
export const CTA_INTENTS = {
    "OPEN_POPUP": "OPEN_POPUP",
    "GET_INFO": "GET_INFO", // contact / mail / phone
    "GO_TO": "GO_TO",
    "UNCLASSIFIED": "UNCLASSIFIED"
}

/**
 * Interface for CTA click event properties
 * Defines the structure for advanced CTA tracking with intent classification
 */
export interface ICtaClickEventProps {
    intent: keyof typeof CTA_INTENTS,
    intentObjectInfo: string,
    ctaContent?: string
}

/**
 * Track advanced CTA clicks with intent classification
 * Provides detailed insights into user interaction patterns and conversion paths
 * Supports multiple GTM click events for comprehensive tracking
 *
 * @param ctaContent - The content/text of the CTA (button text or icon name)
 * @param intent - The classified intent of the CTA action
 * @param intentObjectInfo - Additional context about the CTA target (URL, section ID, etc.)
 */
export function ctaClickEvent({
    ctaContent,
    intent,
    intentObjectInfo
}: ICtaClickEventProps) {
    if (isBrowser()) {
        window.dataLayer?.push({
            event: 'event_click', // Note: Multiple click events exist in GTM, will be optimized in future
            intent: intent,
            intent_object_info: intentObjectInfo,
            cta_content: ctaContent, // Format: icon_{icon_name} || button text
        })
    }
}

/**
 * Classification Guidelines for UNCLASSIFIED CTAs:
 * - For anchor tags: Use href URL or section ID as intentObjectInfo
 * - For other elements: Use tag name + CSS classes as intentObjectInfo
 * This helps in manual classification and future automation improvements
 */


/**
 * GA4 Utility Functions
 *
 * These functions provide utilities for working with Google Analytics 4,
 * including extracting measurement IDs and client IDs for advanced tracking
 * and integration with GA4 Measurement Protocol.
 */

/**
 * Extract GA4 Measurement ID from loaded GTM/GA4 scripts
 * Dynamically finds the GA4 measurement ID from script tags in the DOM
 * Useful for runtime configuration and debugging analytics setup
 *
 * @returns GA4 Measurement ID (format: G-XXXXXXXXXX) or null if not found
 */
export function getMeasurementId() {
    const scripts = Array.from(document?.scripts || []);
    for (const script of scripts) {
        const src = script?.src || '';
        // Match GA4 measurement ID pattern in GTM script URLs
        const match = src?.match(/https:\/\/www.googletagmanager.com\/gtag\/js\?id=(G-[A-Z0-9]+)/);
        if (match?.[1]) return match[1];
    }
    return null;
}

/**
 * Extract GA4 Client ID from browser cookies
 * Retrieves the unique client identifier used by GA4 for user tracking
 * Essential for server-side tracking via GA4 Measurement Protocol
 *
 * The _ga cookie format: GA1.{domain-components}.{client-id-part1}.{client-id-part2}
 * Client ID is constructed from the last two parts of the cookie value
 *
 * @returns GA4 Client ID (format: XXXXXXXXXX.XXXXXXXXXX) or null if not found
 */
export function getClientIdFromCookie() {
    const gaCookie = getClientSideCookie('_ga');

    if (!gaCookie) return null;

    const parts = gaCookie?.split('.');
    if (parts?.length >= 4) {
        return `${parts[2]}.${parts[3]}`; // Extract client_id from cookie parts
    }

    return null;
}