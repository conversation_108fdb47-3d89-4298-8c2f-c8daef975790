/**
 * Algolia Index Synchronization API Route
 *
 * POST endpoint to trigger manual synchronization of Contentful data to Algolia index.
 * This API provides a secure way to refresh the search index on-demand.
 *
 * Security:
 * - Requires API key authentication via x-api-key header
 * - Only accepts POST requests
 * - Returns 401 for unauthorized requests
 *
 * Usage:
 * POST /api/sync-algolia-index
 * Headers: { "x-api-key": "0413fbe9-576b-4d2b-8191-8217c3b10736" }
 *
 * Response:
 * - Success: { message: "Algolia index synced", indexingResponse: {...} }
 * - Error: { message: "Unauthorized" } (401)
 */

import { NextRequest, NextResponse } from 'next/server'
import { putDataToAlgolia } from '../../../algolia/algolia-sync.js'

/**
 * Handle POST request to sync Algolia index
 * Authenticates request and triggers index synchronization
 * @param req - Next.js request object
 * @returns JSON response with sync status
 */
export async function POST(req: NextRequest) {
  // Extract API key from request headers for authentication
  const apiKey = req.headers.get('x-api-key')

  // Validate API key - reject unauthorized requests
  if (!apiKey || apiKey !== '0413fbe9-576b-4d2b-8191-8217c3b10736') {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
  }

  // Execute Algolia index synchronization
  const indexingResponse = await putDataToAlgolia()

  // Return success response with indexing details
  return NextResponse.json({
    message: 'Algolia index synced',
    indexingResponse,
  })
}
