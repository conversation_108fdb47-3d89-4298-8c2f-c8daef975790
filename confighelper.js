import { getConfigrationwithTypeandScopr } from './queryhelper.js'
export async function getConfigurationByScopeAndType(type, scope) {
  try {
    const response = await fetchGraphQL(
      getConfigrationwithTypeandScopr(type, scope),
      false,
      `${scope}`
    )
    const items = response?.data?.configurationsCollection?.items

    if (items && items?.length > 0) {
      const data = await JSON.parse(
        items?.[0]?.data?.json?.content?.[0]?.content?.[0]?.value
      )
      // Return the JSON data from the first matched item
      return data
    } else {
      return null // No matching configuration found
    }
  } catch (error) {
    console.error('Error fetching configuration by scope and type:', error)
    return null
  }
}

/**
 * Configuration Helper GraphQL Fetch Function
 *
 * JavaScript version of the fetchGraphQL function used in configuration scripts
 * and build-time utilities. Provides the same cache tag functionality as the
 * TypeScript version but without type annotations for broader compatibility.
 *
 * This function is used by:
 * - Build-time configuration scripts
 * - Deployment automation tools
 * - Content synchronization utilities
 * - Development helper scripts
 *
 * Cache Management:
 * - Supports revalidation tags for selective cache invalidation
 * - Compatible with Next.js App Router caching system
 * - Enables automated content pipeline workflows
 *
 * @param {string} query - GraphQL query string to execute
 * @param {boolean} preview - Whether to use preview mode
 * @param {string} revalidateTag - Optional cache tag for invalidation
 * @param {number} retries - Number of retry attempts for failed requests
 * @returns {Promise} Promise resolving to GraphQL response data
 */
export async function fetchGraphQL(
  query,
  preview = false,
  revalidateTag,
  retries = 5
) {
  // Override preview mode based on environment configuration
  preview = process.env.NEXT_PUBLIC_PREVIEW === 'true'

  // Resolve Contentful access token from available environment variables
  const contentfulAccessToken =
    process.env.CONTENTFUL_ACCESS_TOKEN ??
    process.env.NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN

  // Configure Next.js cache settings with optional revalidation tag
  let nextConfig = {}
  if (revalidateTag) {
    // Apply cache tag for selective revalidation support
    nextConfig = { tags: [revalidateTag] }
  }

  // Execute GraphQL request with caching configuration
  const response = await fetch(
    `https://graphql.contentful.com/content/v1/spaces/${process.env.NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${process.env.NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}`,
    {
      // cache: 'no-store', // Commented out to enable caching
      next: nextConfig, // Apply cache tags for revalidation
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${contentfulAccessToken}`,
      },
      body: JSON.stringify({ query }),
    }
  )

  let responseData = await response.json()

  if (responseData.errors) {
    if (responseData.errors?.[0]?.message.includes('rate limit')) {
      if (retries > 0) {
        console.error(
          'Retry counter: ',
          5 - retries,
          ' API::error',
          responseData
        )
        await new Promise((resolve) => setTimeout(resolve, 500))
        responseData = await fetchGraphQL(
          query,
          preview,
          revalidateTag,
          retries - 1
        )
      } else {
        throw new Error('API::error: retries exceeded')
      }
    }
  }

  return responseData
}

export function getDomainShortName(domain) {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'agl'
    case 'domainFinanceActiveCom':
      return 'fia'
    case 'domainReonomyCom':
      return 'reo'
    case 'domainVerifinoCom':
      return 'ver'
    case 'domainOne11Com':
      return 'o11'
    default:
      return ''
  }
}
