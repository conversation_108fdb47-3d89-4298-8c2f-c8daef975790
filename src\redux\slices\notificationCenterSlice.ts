import { createSlice } from '@reduxjs/toolkit'
import {
  fetchNotificationsFromStorage,
  NotificationFromFirebase,
} from '../../components/PushNotifications/helpers'

interface Preferences {
  allowedNotificationTypes: string[]
  clearAfter: 'never' | 'weekly' | 'monthly'
  experience: 'yes' | 'no'
  isSoundEnabled: 'false' | 'true'
  noBrowserNoti: 'false' | 'true'
  noExperience: 'false' | 'true'
}

const initialState = {
  localPushNotifications: [] as NotificationFromFirebase[],
  isNotificationCenterOpen: false,
  notificationPermission: 'granted',
  isSafari: false,
  tempNotifications: [] as NotificationFromFirebase[],
  isSaveTempNotis: false,
  isNewNotification: false,
  latestNotifications: [] as NotificationFromFirebase[],
  previousNotifications: [] as NotificationFromFirebase[],
  isAudioPlay: false,
  preferences: {} as Preferences,
}

const notiCenterSlice = createSlice({
  name: 'noti_center',
  initialState,
  reducers: {
    setIsNotificationCenterOpen: (state, action) => {
      state.isNotificationCenterOpen = action.payload
    },
    FetchNotifications: (state, action) => {
      const domain = action.payload

      const latestNotis = fetchNotificationsFromStorage('latest')

      const prevNotis = fetchNotificationsFromStorage('previous')

      state.latestNotifications = latestNotis.filter(
        (item: any) => item.domain === domain
      )

      state.previousNotifications = prevNotis.filter(
        (item: any) => item.domain === domain
      )
    },
    setNotificationPermission: (state, action) => {
      state.notificationPermission = action.payload
    },
    setIsSafari: (state, action) => {
      state.isSafari = action.payload
    },
    setTempNotifications: (state, action) => {
      state.tempNotifications = [...state.tempNotifications, action.payload]
    },
    clearTempNotifications: (state) => {
      state.tempNotifications = []
    },
    setIsSaveTempNotis: (state, action) => {
      state.isSaveTempNotis = action.payload
    },
    setLatestNotifications: (state, action) => {
      state.latestNotifications = action.payload
    },
    setPreviousNotifications: (state, action) => {
      state.previousNotifications = action.payload
    },

    setIsNewNotification: (state, action) => {
      state.isNewNotification = action.payload
    },

    setIsAudioPlay: (state, action) => {
      state.isAudioPlay = action.payload
    },

    setLocalPushNotifications: (state, action: {
      payload: NotificationFromFirebase[]
    }) => {
      state.localPushNotifications = action.payload
    },

    savePreferences: (state, action) => {
      state.preferences = action.payload
    },
  },
})

export const {
  setIsNotificationCenterOpen,
  FetchNotifications,
  setNotificationPermission,
  setIsSafari,
  setTempNotifications,
  clearTempNotifications,
  setIsSaveTempNotis,
  setIsNewNotification,
  setLatestNotifications,
  setIsAudioPlay,
  setPreviousNotifications,
  savePreferences,
  setLocalPushNotifications,
} = notiCenterSlice.actions

export default notiCenterSlice.reducer
