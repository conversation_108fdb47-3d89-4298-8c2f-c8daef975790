import { MetadataRoute } from 'next'
import {
  BUISNESS_DOMAINS_LOCALE_NAVIGATION,
  getDomainShortName,
  TAG_TO_URL,
} from '../globals/utils'
import { fetchGraphQL } from '../lib/api'
import { getConfigurationByScopeAndType } from '../lib/experimentation'
import {
  pageLocaleSlugById,
  pageUrlQueryByLocale,
} from '../lib/queries/page.query'
import { BUISNESS_DOMAINS } from '../utils'
import { getJsonCsvData, getSlugRedirectPath } from '../utils/pageUtils'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const domain = process.env.NEXT_PUBLIC_DOMAIN || ''
  const isPreview = process.env.NEXT_PUBLIC_PREVIEW === 'true'

  let locales = BUISNESS_DOMAINS_LOCALE_NAVIGATION[domain]

  const domainCode = getDomainShortName(domain)?.toUpperCase()

  const configRes = (await getConfigurationByScopeAndType(
    'Locale',
    domainCode
  )) as { allowedLanguages: string[] }

  const allowedLocale = configRes?.allowedLanguages || locales // for the backup incase locale is not configured

  if (domain === 'domainReonomyCom' || domain === 'domainAltusGroupCom') {
    locales = ['en-CA']
  }

  /**
   * Fetches all pages in a locale, including those that are not indexed.
   * @param {string} locale The locale to fetch pages for. Defaults to 'en-CA'.
   * @param {boolean} excludeNoIndexPages If true, pages with noIndex set to true will be excluded.
   * @returns {Promise<Array>} A promise that resolves with an array of all pages in the given locale.
   */
  async function fetchAllPages(locale = 'en-CA', excludeNoIndexPages = false) {
    let allPages: any = []
    let skip = 0
    let recordsFetched = true

    while (recordsFetched) {
      const { data } = await fetchGraphQL(
        pageUrlQueryByLocale(locale, excludeNoIndexPages, 1000, skip),
        true
      )

      // If there are records, add them to the allPages array
      if (
        data?.pageCollection?.items &&
        data?.pageCollection?.items?.length > 0
      ) {
        allPages = [...allPages, ...data.pageCollection.items]
        // Increment skip for the next batch
        skip += 1000
      } else {
        // No more records, stop the loop
        recordsFetched = false
      }
    }

    return allPages
  }

  let redirects = []

  // get the redirects data based on domain
  if (domain === BUISNESS_DOMAINS['finance']) {
    redirects = await getJsonCsvData('fia-redirects.csv')
  }

  if (domain === BUISNESS_DOMAINS['verifino']) {
    redirects = await getJsonCsvData('ver-redirects.csv')
  }

  let allPages: any = []

  // fetch the all pages for each locale from contentful locale configuration
  for (const locale of allowedLocale) {
    const pages = await fetchAllPages(locale, true)

    allPages = [...allPages, ...pages]
  }

  const resD = await getConfigurationByScopeAndType(
    'Experimentation',
    `${getDomainShortName(process.env.NEXT_PUBLIC_DOMAIN!)?.toUpperCase()}`
  )

  resD?.data?.forEach((expPage) => {
    if (
      expPage?.masterPage &&
      expPage?.experimentationId &&
      !expPage?.isDeleted &&
      (isPreview || expPage?.isPublished) &&
      expPage?.experimentationPages &&
      expPage?.experimentationPages?.length
    ) {
      allPages.push({
        publishDate: expPage?.updatedAt ?? expPage?.date,
        slug: expPage?.masterPage,
      })
      // console.log(
      //   {
      //     publishDate: expPage?.updatedAt ?? expPage?.date,
      //     slug: expPage?.masterPage,
      //   },
      //   'Exp  >>> ExpPAge'
      // )
    }
  })

  const domainUrl = TAG_TO_URL[domain as keyof typeof TAG_TO_URL]

  /**
   * Given a page object, generate the alternative urls for the given locales
   * @param page The page object from contentful
   * @returns An object with language codes as keys and the corresponding alternative urls as values
   */
  const generateAlternatives = async (page: any) => {
    let res: { [key: string]: string } = {}
    if (domain === BUISNESS_DOMAINS['altus']) {
      allowedLocale.forEach((locale) => {
        if (locale === 'en-CA') return
        const onlyLang = locale?.split('-')?.[0]

        const slug = page?.slug

        res[onlyLang] =
          `${domainUrl}${slug === '/' ? '' : '/' + slug}?lang=${onlyLang}`
      })
    } else if (domain !== BUISNESS_DOMAINS['reonomy']) {
      const allLocaleSlugsResponse = await fetchGraphQL(
        pageLocaleSlugById(page?.sys?.id),
        true
      )

      const allLocaleSlugsData = allLocaleSlugsResponse?.data ?? {}

      allowedLocale.forEach((locale) => {
        if (locale === 'en-CA') return
        const onlyLang = locale?.split('-')?.[0]

        const slug = allLocaleSlugsData?.[onlyLang]?.slug

        res[onlyLang] =
          `${domainUrl}/${getLocaleSlugWithRedirectPath({ slug })}`
      })
    }
    return res
  }

  /**
   * Given a slug, get the slug with redirect path if it exists, otherwise return the original slug.
   * @param {{ slug: string }} obj The object containing the slug
   * @returns The slug with redirect path if it exists, otherwise the original slug
   */
  const getLocaleSlugWithRedirectPath = ({ slug }: { slug: string }) => {
    const redirectedSlug = getSlugRedirectPath({ jsonData: redirects, slug })

    return redirectedSlug || (slug === '/' ? '' : slug)
  }

  const allPagePromiseArray = allPages?.map(async (page) => {
    return {
      url: encodeURI(
        `${domainUrl}${page.slug === '/' ? '' : '/' + page.slug}/`
      ),
      lastModified: page?.publishDate
        ? new Date(page?.publishDate).toISOString()
        : page?.sys?.publishedAt,
      alternates: {
        languages: await generateAlternatives(page),
      },
    }
  })

  const sitemapArray = await Promise.all(allPagePromiseArray)

  return sitemapArray
}
