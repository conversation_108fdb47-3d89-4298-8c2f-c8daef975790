# ReonomyFreeTrial Form Template Documentation

## Overview

The ReonomyFreeTrial Form Template is a specialized multi-step form component designed specifically for Reonomy product trial registrations. This form integrates with Reonomy's API for user account creation, includes sophisticated email validation, and provides a seamless trial signup experience with dual integration to both Reonomy and Pardot systems.

### Key Features
- **Multi-Step Form Flow**: Progressive form completion with step navigation
- **Reonomy API Integration**: Direct integration with Reonomy signup and validation APIs
- **Dual Submission**: Reonomy account creation followed by Pardot lead tracking
- **Email Pre-validation**: Real-time email validation against Re<PERSON>'s database
- **Password Security**: Minimum 8-character password requirement
- **Geographic Restrictions**: Limited to US and Canada users
- **Error Handling**: Comprehensive error handling for duplicate accounts and validation failures
- **Analytics Tracking**: Complete form interaction and conversion tracking

## Form Structure

### Component Location
```
src/systems/ReonomyForms/ReonomyFreeTrailForm/index.tsx
```

### Form Template Configuration
```typescript
interface ReonomyFormData {
  template: 'ReonomyFreeTrial'
  endpointUrl: string          // Pardot endpoint for lead tracking
  sfmcUrl: string             // SFMC integration URL
  formCategory: string        // Form categorization
  formFieldsCollection: {
    items: FormSection[]      // Multi-step form sections
  }
  thankYouMessage: RichText   // Success confirmation message
  loadingMessage: RichText    // Loading state message
  successMessage: RichText    // Success state message
  errorMessage: RichText      // Error state message
}
```

## Form Fields and Validation

### Required Form Fields

#### 1. Email Field
- **Field Type**: `Email`
- **Validation**: Real-time email validation via Reonomy API
- **API Endpoint**: `https://app.reonomy.com/signup-prevalidation`
- **Debounce**: 300ms delay for validation calls
- **Error Handling**: Duplicate email detection

```typescript
// Email validation structure
{
  altusFieldType: "Email",
  altusFieldName: "email",
  altusLabel: "Email Address",
  altusIsRequired: true,
  placeholderText: "Enter your email address",
  validationErrorMessage: "Please enter a valid email address",
  isAltusEmailAllowed: false,
  isGenericEmailAllowed: true
}
```

#### 2. First Name Field
- **Field Type**: `Text`
- **Validation**: Required field validation
- **Max Length**: Standard text field limits

```typescript
{
  altusFieldType: "Text",
  altusFieldName: "first_name",
  altusLabel: "First Name",
  altusIsRequired: true,
  placeholderText: "Enter your first name",
  validationErrorMessage: "First name is required"
}
```

#### 3. Last Name Field
- **Field Type**: `Text`
- **Validation**: Required field validation

```typescript
{
  altusFieldType: "Text",
  altusFieldName: "last_name",
  altusLabel: "Last Name",
  altusIsRequired: true,
  placeholderText: "Enter your last name",
  validationErrorMessage: "Last name is required"
}
```

#### 4. Password Field
- **Field Type**: `Password`
- **Validation**: Minimum 8 characters
- **Security**: Password excluded from Pardot submission

```typescript
{
  altusFieldType: "Password",
  altusFieldName: "password",
  altusLabel: "Password",
  altusIsRequired: true,
  placeholderText: "Create a password (min 8 characters)",
  validationErrorMessage: "Password must be at least 8 characters",
  helperText: "Password must be at least 8 characters long"
}
```

#### 5. Phone Field
- **Field Type**: `Text` with phone formatting
- **Validation**: Phone number format validation
- **Error Handling**: Duplicate phone number detection

```typescript
{
  altusFieldType: "Text",
  altusFieldName: "phone",
  altusLabel: "Phone Number",
  altusIsRequired: true,
  placeholderText: "Enter your phone number",
  validationErrorMessage: "Please enter a valid phone number"
}
```

### Geographic Restrictions

The form is restricted to users in specific countries:

```typescript
export const reonomyFreeTrialCountries = [
  { country: 'United States' },
  { country: 'Canada' }
]
```

## Multi-Step Form Flow

### Step Navigation Logic

```typescript
// Step state management
const [currentStep, setCurrentStep] = useState(0)

// Navigation functions
const nextStep = () => {
  setCurrentStep((prevStep) => prevStep + 1)
}

const prevStep = () => {
  setCurrentStep((prevStep) => Math.max(prevStep - 1, 0))
}

// Step validation and progression
const handleStepSubmission = (e) => {
  e.preventDefault()
  if (currentStep + 1 === section.length) {
    handleSubmitMain(values)  // Final submission
  } else {
    nextStep()  // Move to next step
  }
}
```

### Step Classes and Animation

```typescript
// CSS classes for step transitions
const carouselClass = (currentStep: number, index: number) => {
  if (currentStep === index) return 'form-active'
  if (currentStep > index) return 'form-left'
  if (currentStep < index) return 'form-right'
  return ''
}
```

## API Integration Flow

### 1. Email Pre-validation

```typescript
// Email validation API call
const checkEmail = async (email: string) => {
  const response = await fetch('/api/form/reonomy-email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  })
  const data = await response.json()
  return data
}
```

**API Endpoint**: `/api/form/reonomy-email`
**External API**: `https://app.reonomy.com/signup-prevalidation`

### 2. Reonomy Account Creation

```typescript
// Reonomy signup API integration
export function postToReonomy({
  values,
  setFormSubmitionState,
  setFormSubmitionStateErrorMsg,
  sfmcUrl,
  endpointUrl,
  contentfulId,
  formCategory,
  formData
}) {
  const reqBody = JSON.stringify({
    email: values?.email,
    first_name: values?.first_name,
    last_name: values?.last_name,
    password: values?.password,
    remote_addr: '0.0.0.0',
    phone: values?.phone,
    accept_agreements: ['466c3907-63ee-49fd-b2b2-a225c5765462']
  })

  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: reqBody,
    redirect: 'follow'
  }

  fetch('/api/form/reonomy-signup', requestOptions)
    .then(response => response.json())
    .then(response => {
      if (response?.data?.auth && response?.data?.otk && response?.data?.user_id) {
        // Success: Account created
        setFormSubmitionStateErrorMsg('')
        
        // Proceed to Pardot integration
        postToPardot({
          values,
          setFormSubmitionState,
          sfmcUrl,
          endpointUrl,
          contentfulId,
          formData
        })
      } else {
        // Handle errors
        handleReonomyErrors(response)
      }
    })
}
```

**API Endpoint**: `/api/form/reonomy-signup`
**External API**: `https://app.reonomy.com/api/signup`

### 3. Success Response Structure

```typescript
// Successful Reonomy response
{
  status: 'success',
  data: {
    auth: 'authentication_token',
    otk: 'one_time_key',
    user_id: 'unique_user_identifier'
  }
}
```

### 4. Error Handling

```typescript
// Error response handling
const handleReonomyErrors = (response) => {
  let error = ''
  
  if (response?.data?.code === 'PHONE_ALREADY_IN_USE') {
    error = 'This phone number is already in use.'
  } else if (response?.data?.code === 'DUPLICATE_ERROR') {
    error = 'There is already an account associated with this email address.'
  } else if (response?.data?.code === 'INVALID_EMAIL') {
    error = 'Please enter a valid email address.'
  } else if (response?.data?.code === 'WEAK_PASSWORD') {
    error = 'Password must be at least 8 characters long.'
  } else {
    error = 'An error occurred during registration. Please try again.'
  }
  
  setFormSubmitionStateErrorMsg(error)
  setFormSubmitionState('FAILED')
}
```

## Form Submission States

### State Management

```typescript
type FormSubmissionState = 
  | 'NOT SUBMITTED'  // Initial state
  | 'LOADING'        // Processing submission
  | 'SUCCESS'        // Reonomy account created successfully
  | 'FAILED'         // Submission failed
  | 'THANK YOU'      // Final success state

const [formSubmitionState, setFormSubmitionState] = useState<FormSubmissionState>('NOT SUBMITTED')
```

### State Transitions

1. **NOT SUBMITTED** → **LOADING**: User submits form
2. **LOADING** → **SUCCESS**: Reonomy account created successfully
3. **LOADING** → **FAILED**: Reonomy API returns error
4. **SUCCESS** → **THANK YOU**: Pardot integration completed
5. **FAILED** → **NOT SUBMITTED**: User dismisses error and retries

## Data Processing and Mapping

### Form Data Collection

```typescript
const handleSubmitMain = useCallback((values) => {
  setFormSubmitionState('LOADING')
  
  // Collect URL parameters from session storage
  const urlParamsValues = JSON.parse(
    getSessionStorageItem('__params') ?? '{}'
  )
  
  // Merge form values with tracking data
  const formValues = {
    utm_campaign: 'none',
    utm_content: 'none',
    utm_term: 'none',
    utm_medium: 'website-direct',
    utm_source: 'none',
    ...urlParamsValues,
    ...values,
    sourceUrl: window.location.href,
    formid: updatedProps.data?.sys?.id
  }
  
  // Submit to Reonomy (which then triggers Pardot)
  postToReonomy({
    values: formValues,
    setFormSubmitionState,
    sfmcUrl: updatedProps.data?.sfmcUrl,
    endpointUrl: updatedProps.data?.endpointUrl,
    contentfulId: updatedProps.data?.sys?.id,
    formCategory: updatedProps.data?.formCategory
  })
}, [updatedProps])
```

### Field Mapping for Pardot Integration

```typescript
// Field name transformations for Pardot
const fieldMapping = {
  'company_name': 'company',
  'job_title': 'jobtitle',
  'email': 'Email',
  'first_name': 'firstname',
  'last_name': 'lastname'
}

// Password is excluded from Pardot submission for security
const sanitizedValues = { ...values }
delete sanitizedValues['password']
```

## User Experience Features

### Loading States

```jsx
// Loading spinner during submission
{formSubmitionState === 'LOADING' && (
  <SpinnerToast
    simpleParagraph={{
      data: updatedProps?.data?.loadingMessage?.content
    }}
    setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
    htmlAttr={{ style: { width: '100%', maxWidth: '900px' } }}
  />
)}
```

### Success Confirmation

```jsx
// Success message display
{formSubmitionState === 'SUCCESS' && (
  <SuccessToast
    simpleParagraph={{
      data: updatedProps?.data?.successMessage?.content
    }}
    setToastState={() => null}
    htmlAttr={{ style: { width: '100%', maxWidth: '900px' } }}
  />
)}
```

### Error Display

```jsx
// Error message display
{formSubmitionState === 'FAILED' && (
  <ErrorToast
    setToastState={() => setFormSubmitionState('NOT SUBMITTED')}
    simpleParagraph={{
      data: updatedProps?.data?.errorMessage?.content
    }}
    htmlAttr={{ style: { width: '100%', maxWidth: '900px' } }}
  />
)}
```

## Security Considerations

### Password Handling
- **Client-side**: Minimum 8-character validation
- **Transmission**: Sent securely to Reonomy API
- **Storage**: Password excluded from Pardot submission
- **Security**: Never stored in client-side state after submission

### Data Privacy
- **Agreement Acceptance**: Automatic acceptance of Reonomy terms
- **Agreement ID**: `466c3907-63ee-49fd-b2b2-a225c5765462`
- **IP Address**: Set to `0.0.0.0` for privacy
- **Data Retention**: Follows Reonomy and Pardot data policies

## Analytics Integration

### Form Events Tracked

```typescript
// Form start tracking
{
  event: 'custom_form_start',
  formId: contentfulFormId,
  formTemplate: 'ReonomyFreeTrial',
  formCategory: 'product-trial',
  timestamp: new Date().toISOString()
}

// Form success tracking
{
  event: 'custom_form_success',
  formId: contentfulFormId,
  formTemplate: 'ReonomyFreeTrial',
  formCategory: 'product-trial',
  reonomyUserId: response.data.user_id,
  timestamp: new Date().toISOString()
}

// Form error tracking
{
  event: 'custom_form_error',
  formId: contentfulFormId,
  formTemplate: 'ReonomyFreeTrial',
  errorType: response.data.code,
  errorMessage: errorMessage,
  timestamp: new Date().toISOString()
}
```

## Testing and Validation

### Email Validation Testing
- **Valid Emails**: Standard email format validation
- **Duplicate Detection**: Real-time check against Reonomy database
- **Domain Restrictions**: Generic email providers allowed
- **Debouncing**: 300ms delay to prevent excessive API calls

### Form Validation Testing
- **Required Fields**: All fields must be completed
- **Password Strength**: Minimum 8 characters enforced
- **Phone Format**: Valid phone number format required
- **Step Progression**: Cannot advance without completing current step

### API Integration Testing
- **Reonomy Signup**: Account creation success/failure scenarios
- **Email Pre-validation**: Duplicate email detection
- **Pardot Integration**: Lead tracking after successful signup
- **Error Handling**: All error codes properly handled and displayed

## Deployment Configuration

### Environment Variables

```bash
# Reonomy API Configuration
REONOMY_SIGNUP_API=https://app.reonomy.com/api/signup
REONOMY_VALIDATION_API=https://app.reonomy.com/signup-prevalidation

# Pardot Integration
PARDOT_ENDPOINT_URL=https://go.pardot.com/your-endpoint
SFMC_ENDPOINT_URL=https://cloud.mc.exacttarget.com/your-endpoint

# Analytics
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
```

### API Endpoints

```typescript
// Internal API routes
'/api/form/reonomy-email'     // Email validation proxy
'/api/form/reonomy-signup'    // Account creation proxy

// External API endpoints
'https://app.reonomy.com/signup-prevalidation'  // Email validation
'https://app.reonomy.com/api/signup'            // Account creation
```

---

*This documentation covers the complete ReonomyFreeTrial form template implementation, including API integration, validation, error handling, and user experience features. For technical support or modifications, contact the development team.*
