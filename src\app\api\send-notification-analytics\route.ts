import { ENV_VARS } from '../../../globals/utils'

const analyticsConfig = {
  apiSecret: ENV_VARS.FIREBASE_API_SECRET,
  measurementId: ENV_VARS.FIREBASE_MEASUREMENT_ID,
}

export async function POST(req: Request) {
  const body = await req.json()
  console.log('body at analytics: ', body)

  const analyticsUrl = `https://www.google-analytics.com/mp/collect?measurement_id=${analyticsConfig.measurementId}&api_secret=${analyticsConfig.apiSecret}`

  try {
    /**
     * Send Notification Analytics to GA4
     *
     * Forwards notification interaction events to GA4 using the Measurement Protocol.
     * This enables tracking of push notification engagement (clicks, dismissals)
     * for measuring notification effectiveness and user engagement patterns.
     *
     * The request body should contain:
     * - client_id: GA4 client identifier (from _ga cookie or generated)
     * - events: Array of notification events with interaction data
     */
    const res = await fetch(analyticsUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    // Log GA4 API response for debugging
    console.log('GA4 response status:', res.status, 'OK?', res.ok)

    // Handle GA4 API errors
    if (!res.ok) {
      const text = await res.text()
      console.error('GA4 error response:', text)
      return new Response(JSON.stringify({ error: text }), {
        status: res.status,
      })
    }
  } catch (error: any) {
    console.error('Error sending analytics:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
    })
  }

  // Return success response
  return new Response(JSON.stringify({ message: 'analytics sent' }), {
    status: 200,
  })
}
