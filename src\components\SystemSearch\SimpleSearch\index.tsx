'use client'
// Algolia search imports
import algoliasearch from 'algoliasearch/lite'
import { useState } from 'react'
import { InstantSearch } from 'react-instantsearch'
// Component imports for search functionality
import SearchInput from '../../Controls/SearchInput'
import Kernel from '../../Kernel'
import SearchActive from '../SearchActive'
import SearchDefault from '../SearchDefault'
import { SimpleSearchD } from './defaults'
import style from './index.module.scss'
import { SimpleSearchI } from './interface'

/**
 * SimpleSearch Component
 *
 * Main search component that provides the complete search experience including:
 * - Search input field with real-time suggestions
 * - Default state showing recent searches and recommendations
 * - Active state showing search results and related content
 * - Integration with Algolia InstantSearch for real-time search
 *
 * The component uses Algolia's InstantSearch wrapper to provide:
 * - Real-time search as user types
 * - Search result highlighting
 * - Faceted search capabilities
 * - Analytics tracking
 */

// Initialize Algolia search client with lite version for better performance
// Lite version excludes some advanced features but is smaller and faster
const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)

// Algolia index name for search operations
// This determines which index to search against in Algolia
const searchIndexName = process.env.NEXT_PUBLIC_ALGOLIA_INDEX as string

export default function SimpleSearch(props: SimpleSearchI) {
  let className = `${props.isLightMode ? style.light : style.dark} ${props.htmlAttr?.className ?? ''
    }`

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: SimpleSearchI = {
    ...SimpleSearchD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: className,
      style: props.htmlAttr?.style,
    },
  }

  // State to track whether search is active (user has entered search query)
  // Controls whether to show SearchDefault (empty state) or SearchActive (results state)
  const [isActive, setIsActive] = useState<boolean>(false)

  /**
   * Handle search input changes
   * Switches to active search state when user starts typing
   * This triggers the display of search results instead of default content
   * @param val - Input value (not used in current implementation)
   */
  const inpuHandler = (val) => {
    setIsActive(true)
  }

  /**
   * Handle search input clear action
   * Resets search to default state when clear button is clicked
   * This shows recent searches and recommendations instead of search results
   */
  const onInputClearHandler = () => {
    setIsActive(false)
  }

  return (
    // Kernel wrapper provides consistent styling and layout structure
    <Kernel {...updatedProps} isLightMode={false}>
      {/*
        InstantSearch wrapper connects all search components to Algolia
        - indexName: Specifies which Algolia index to search
        - searchClient: Configured Algolia client for API communication
      */}
      <InstantSearch indexName={searchIndexName} searchClient={searchClient}>
        {/*
          SearchInput component provides the search input field with:
          - Real-time search as user types
          - Clear button functionality
          - Integration with Algolia's search state
        */}
        <SearchInput
          {...updatedProps.searchInput}
          htmlAttr={{ onChange: inpuHandler }}
          closeIcon={{
            ...updatedProps.searchInput?.closeIcon,
            htmlAttr: { onClick: onInputClearHandler },
          }}
          isLightMode={updatedProps.isLightMode}
        />
        {/*
          Conditional rendering based on search state:
          - SearchActive: Shows when user has entered search query (search results)
          - SearchDefault: Shows when search is empty (recent searches, recommendations)
        */}
        {isActive ? (
          <SearchActive
            {...updatedProps.activeSearch}
            isLightMode={updatedProps.isLightMode}
            isFullXBleed
          />
        ) : (
          <SearchDefault
            {...updatedProps.defaultSearch}
            isLightMode={updatedProps.isLightMode}
            isFullXBleed
          />
        )}
      </InstantSearch>
    </Kernel>
  )
}
