import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { DynamicEventWidgetI } from '../../components/ArticleLists/DynamicEventWidget/interface'
import { DynamicPressReleaseI } from '../../components/ArticleLists/DynamicPressRelease/interface'
import { DynamicWidgetFeaturedI } from '../../components/Cards/CardFeatured/DynamicWidgetFeatured/interface'
import { DynamicWidgetInsightsI } from '../../components/Cards/CardGeneric/DynamicWidgetInsights/interface'
import { DynamicWidgetOurTeamI } from '../../components/Cards/CardPeople/DynamicWidgetOurTeam/interface'
import { HeroInsightsCarouselI } from '../../components/Heros/HeroInsigths/@Core/HeroInsightsCarousel/interface'
import { HeroInsightsI } from '../../components/Heros/HeroInsigths/interface'
import {
  convertStringToCamelCase,
  extractValues,
  l,
  useTranslation,
  valueExists,
} from '../../globals/utils'
import { fetchGraphQL } from '../api'
import { getCardFromTagsQuery } from '../queries/card.query'
import {
  getPageFromTagsHeroQuery,
  getPageFromTagsQuery,
} from '../queries/page.query'
import { getPeopleProps } from './card.mapping'
import { getUpdatedHrefFromSlug } from './link.mapping'

export async function getDynamicWidgetInsightsProps(
  props: unknown
): Promise<DynamicWidgetInsightsI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    // get each tag from the array and convert it to camel case and push it to the array
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  if (props?.condition !== 'OR' && !tags.includes('afsInsights'))
    // if the condition is AND and the tag is not present in the array, add it
    tags.unshift(`"afsInsights"`)

  // fetch data from contentful
  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 6,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all',
      props?.locale,
      'afsInsights'
    ),
    true,
    'dts'
  )

  // map the data to the component props
  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        heading: {
          textContent:
            componentData?.afsCardTitle || componentData?.title || '',
        },
        thumbnail: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href:
            '/' + getUpdatedHrefFromSlug(componentData?.slug, props.locale) ||
            '',
          textContent: useTranslation('readMore', props?.locale),
          target: '_self',
          variant: 'tertiary',
          isIconPrefixed: false,
          isChevron2Arrow: true,
        },
        date: {
          textContent: moment(componentData?.publishDate?.split('T')[0])
            .locale(props?.locale || 'en')
            .format('MMM D, YYYY'),
        },
        htmlAttr: { className: 'shadow3' },
      }
    }
  )

  // return the component props
  const dynamicWidgetInsightsProps: DynamicWidgetInsightsI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    Cards: cards,
    button: props?.button,
    htmlAttr: props?.htmlAttributes,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicWidgetFeaturedProps(
  props: unknown
): Promise<DynamicWidgetFeaturedI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    // convert the tags to camel case and push it to the array
    const element = convertStringToCamelCase(props?.tags?.at(i))
    l(`"${element}"`)
    tags.push(`"${element}"`)
  }

  //  fetch data from contentful
  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true,
    'dts'
  )

  // map the data to the component props
  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        contextualInformation: {
          heading: {
            textContent: componentData?.afsCardTitle || componentData?.title,
          },
          subHeading: {
            textContent:
              componentData?.afsDescription || componentData?.shortTitle,
          },
          excerpt: { data: { type: 'doc' } },
        },
        orientation: 'portrait',
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href: '/' + componentData?.slug || '',
          textContent: useTranslation('learnMore', props?.locale),
          target: componentData?.target,
          variant: 'tertiary3',
          isIconPrefixed: true,
          isChevron2Arrow: true,
          isLightMode: props?.isLightMode,
        },
      }
    }
  )

  // return the component props
  const dynamicWidgetInsightsProps: DynamicWidgetFeaturedI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    cards: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

/**
 * Fetches and assembles the dynamic hero insights properties by querying
 * GraphQL endpoints with specified tags and conditions. It merges the responses
 * from the main query and the highlight query to organize the data into hero
 * and card components.
 *
 * @param props - The properties object containing configuration for the query:
 *   - tags: An array of tags used to filter the data.
 *   - limit: The maximum number of items to fetch.
 *   - orderBy: The order in which to sort the results.
 *   - condition: The condition ('OR' or 'AND') for tag queries.
 *   - locale: The locale for translations and date formatting.
 *   - isLightMode: A boolean to determine the theme mode.
 *   - breadCrumb: Breadcrumb data.
 *   - subHeading: Text for the subheading.
 *   - heading: Text for the main heading.
 *   - description: The description content.
 *   - button: Button configuration.
 *
 * @returns A Promise that resolves to a HeroInsightsI object containing
 * the assembled properties for hero insights including hero carousels and
 * generic cards.
 */

export async function getDynamicHeroInsightsProps(
  props: unknown
): Promise<HeroInsightsI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    // convert the tags to camel case and push it to the array
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let condition =
    props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'

  //  fetch data from contentful
  let response: any = await fetchGraphQL(
    getPageFromTagsHeroQuery(
      props?.limit || 10,
      props?.orderBy,
      `{
      ${condition}: [${tags}],
      id_contains_none: ["dtsInsightsHeroHighlight"],
      id_contains_all: [
        "${process.env.NEXT_PUBLIC_DOMAIN}",
      ],
    }`
    ),
    true,
    'dts'
  )

  //  fetch data from contentful for DTSHeroInsights
  let responseHighlight = await fetchGraphQL(
    getPageFromTagsHeroQuery(
      4,
      props?.orderBy,
      `{
      ${condition}: [${tags}],
      id_contains_all: [
        "dtsInsightsHeroHighlight",
        "${process.env.NEXT_PUBLIC_DOMAIN}",
      ],
    }`
    ),
    true,
    'dts'
  )

  let hero: HeroInsightsCarouselI[] = []
  let cards: [] = []

  response = {
    ...response,
    data: {
      ...response?.data,
      pageCollection: {
        ...response?.data?.pageCollection,
        items: response?.data?.pageCollection?.items?.concat(
          responseHighlight?.data?.pageCollection?.items
        ),
      },
    },
  }

  // map the data to the component props
  for (let i = 0; i < response?.data?.pageCollection?.items?.length; i++) {
    const componentData = response?.data?.pageCollection.items[i]
    let obj = {
      isLightMode: props?.isLightMode,
      heading: {
        textContent: componentData?.afsCardTitle || componentData?.title,
      },
      thumbnail: {
        src: componentData?.pageThumbnail?.url,
        alt: componentData?.pageThumbnail?.name,
        objectFit: 'cover',
        height: '100%',
        width: '100%',
      },
      cta: {
        href: '/' + componentData?.slug || '',
        textContent: useTranslation('readMore', props?.locale),
        target: '_self',
        variant: 'tertiary3',
        isIconPrefixed: true,
        isChevron2Arrow: true,
        isLightMode: props?.isLightMode,
      },
      date: {
        textContent: moment(props?.startTime?.split('T')[0])
          .locale(props?.locale)
          .format('MMM D, YYYY'), // using moment js
      },
      excerpt: {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  marks: [
                    {
                      type: 'textStyle',
                      attrs: {
                        color: '',
                      },
                    },
                  ],
                  text:
                    componentData?.afsDescription ||
                    componentData?.seoDescription ||
                    '',
                },
              ],
            },
          ],
        },
      },
    }

    if (
      componentData?.contentfulMetadata?.tags.some((obj) =>
        valueExists(obj, 'name', 'DTS: Insights Hero Highlight')
      )
    ) {
      if (hero.length < 4) {
        hero.push(obj)
      } else {
        cards.push(obj)
      }
    } else {
      cards.push(obj)
    }
  }

  // return the component props
  const heroInsightsProps: HeroInsightsI = {
    isLightMode: props?.isLightMode,
    breadcrumbs: props?.breadCrumb,
    heading: {
      textContent: props?.subHeading || '',
    },
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      showButtons: false,
      excerpt: { data: props?.description },
    },
    cardCarousel: {
      isProgressBar: true,
      isLightMode: props?.isLightMode,
      carouselData: hero,
    },
    cardGeneric: cards,
    button: props?.button,
  }
  return heroInsightsProps
}

/**
 * Given a list of tags, fetch the relevant content from the cms and map it to the props for the DynamicPressRelease component.
 *
 * @param {unknown} props The props for the component.
 * @returns {Promise<DynamicPressReleaseI>} The component props.
 */
export async function getDynamicPressReleaseProps(
  props: unknown
): Promise<DynamicPressReleaseI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true,
    'dts'
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        contextualInformation: {
          heading: {
            textContent: componentData?.title,
          },
          subHeading: {
            textContent: componentData?.shortTitle,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text: componentData?.seoDescription || '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(props?.startTime?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
    }
  )

  const dynamicWidgetInsightsProps: DynamicPressReleaseI = {
    isLightMode: props?.isLightMode,
    articleitems: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

/**
 * Function to fetch props for DynamicEventWidget component.
 *
 * @param {unknown} props - Props passed from the parent component.
 * @returns {Promise<DynamicEventWidgetI>} - Props for the DynamicEventWidget component.
 */
export async function getDynamicEventWidgetProps(
  props: unknown
): Promise<DynamicEventWidgetI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  if (props?.condition !== 'OR' && !tags.includes('afsPressRelease'))
    tags.unshift(`"afsPressRelease"`)

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all',
      props?.locale,
      'afsPressRelease'
    ),
    true,
    'dts'
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      let data123 = {
        isLightMode: props?.isLightMode,
        type: props?.shortTitle || 'Event',
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href: componentData.slug,
        },
        contextualInformation: {
          heading: {
            textContent: componentData?.afsCardTitle || componentData?.title,
          },
          subHeading: {
            textContent:
              componentData?.afsDescription || componentData?.shortTitle,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text:
                        componentData?.afsDescription ||
                        componentData?.seoDescription ||
                        '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(componentData?.publishDate?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
      return data123
    }
  )

  const dynamicWidgetInsightsProps: DynamicEventWidgetI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    articleitems: cards,
    htmlAttr: props?.htmlAttributes,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

/**
 * Function to fetch props for DynamicMediaHighlights component.
 *
 * @param {unknown} props - Props passed from the parent component.
 * @returns {Promise<DynamicEventWidgetI>} - Props for the DynamicMediaHighlights component.
 */
export async function getDynamicMediaHighlightsProps(
  props: unknown
): Promise<DynamicEventWidgetI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true,
    'dts'
  )

  const cards = response?.data?.pageCollection.items.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        contextualInformation: {
          heading: {
            textContent: componentData?.afsCardTitle || componentData?.title,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text:
                        componentData?.afsDescription ||
                        componentData?.seoDescription ||
                        '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(props?.startTime?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
    }
  )

  const dynamicWidgetInsightsProps: DynamicEventWidgetI = {
    isLightMode: props?.isLightMode,
    articleitems: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

/**
 * Given a list of tags, fetch the relevant content from the cms and map it to the props for the DynamicWidgetOurTeam component.
 *
 * @param {unknown} props The props for the component.
 * @returns {Promise<DynamicWidgetOurTeamI>} The component props.
 */
export async function getDynamicWidgetOurTeamProps(
  props: unknown
): Promise<DynamicWidgetOurTeamI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let responsePage = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      'title_ASC',
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true,
    'dts'
  )
  let responseTeam = await fetchGraphQL(
    getCardFromTagsQuery(
      props?.tags?.map((e: string) => `"${e}"`) || [],
      props?.limit || 3,
      'fullName_ASC',
      props?.condition === 'OR' ? 'tags_contains_some' : 'tags_contains_all'
    ),
    true,
    'dts'
  )

  const pages = responsePage?.data?.pageCollection.items.map(
    (componentData: any) => {
      let cardTags = []

      if (
        componentData?.contentfulMetadata?.tags &&
        componentData?.contentfulMetadata?.tags?.length !== 0
      ) {
        cardTags = componentData?.contentfulMetadata?.tags?.map(
          (tag: string) => ({ text: { textContent: extractValues(tag.name) } })
        )
      }
      return {
        isLightMode: props?.isLightMode,
        person: {
          fullName: {
            textContent:
              componentData?.afsCardTitle || componentData?.title || '',
          },
          jobTitle: { textContent: componentData?.shortTitle || '' },
          avatar: {
            src: componentData?.pageThumbnail?.url || '',
            alt: componentData?.pageThumbnail?.title || '',
            height: '100%',
            width: '100%',
            objectFit: 'cover',
          },
          bio: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      text: '',
                    },
                  ],
                },
              ],
            },
          },
        },
        tags: cardTags || [],
        orientation: 'portrait',
        showButton: true,
        cta: {
          href: '/' + componentData?.slug || '',
          textContent: 'View Profile',
          target: '_self',
          variant: 'tertiary3',
          isIconPrefixed: true,
          isChevron2Arrow: true,
        },
      }
    }
  )

  const cards = responseTeam?.data?.cardComponentCollection.items.map(
    (componentData: any) => {
      return getPeopleProps({
        ...componentData,
        isLightMode: props?.isLightMode,
        orientation: 'portrait',
        description: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: '',
                },
              ],
            },
          ],
        },
      })
    }
  )

  const dynamicWidgetOurTeamProps: DynamicWidgetOurTeamI = {
    isLightMode: props?.isLightMode,
    card: pages.concat(cards),
    button: props?.button,
  }

  return dynamicWidgetOurTeamProps
}
