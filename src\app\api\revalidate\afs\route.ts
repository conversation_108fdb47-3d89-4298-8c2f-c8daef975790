/**
 * Advanced Filtering System (AFS) Cache Revalidation API Route
 *
 * This endpoint handles cache invalidation for AFS (Advanced Filtering System) pages
 * across multiple domains. It processes Contentful webhook payloads containing
 * domain and AFS tags, then triggers revalidation on the appropriate domain's
 * deployment using cross-domain API calls.
 *
 * Multi-Domain Architecture:
 * - Supports multiple business domains (Altus, Reonomy, etc.)
 * - Routes revalidation requests to correct domain deployments
 * - Handles domain-specific AFS configurations and slugs
 *
 * Contentful Integration:
 * - Processes webhook metadata tags from Contentful
 * - Extracts domain and AFS identifiers from tag structure
 * - Maps AFS tags to corresponding page slugs for revalidation
 *
 * Cross-Domain Revalidation:
 * - Makes HTTP requests to target domain's revalidation API
 * - Forwards revalidation requests with appropriate slug arrays
 * - Handles response forwarding and error propagation
 */

import { NextRequest } from 'next/server'

/**
 * Type definition for Contentful tag structure
 * Used to extract domain and AFS identifiers from webhook metadata
 */
type cfTag = {
  sys: {
    id: string
  }
}

/**
 * Handle POST request for AFS-specific cache revalidation
 * Processes Contentful webhook with domain and AFS tags for cross-domain revalidation
 * @param request - Next.js request object containing Contentful webhook payload
 * @returns JSON response with revalidation status and details
 */
export async function POST(request: NextRequest) {
  const req = await request.json()

  // Extract domain and AFS tags from Contentful webhook metadata
  // Domain tag identifies which business domain to target
  const domainTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('domain')
  )?.sys?.id

  // AFS tag identifies the specific filtering system configuration
  const afsTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('afs')
  )?.sys?.id

  console.log('POST: revalidate afs', domainTag, afsTag)

  // Validate that AFS tag is present for revalidation
  if (!afsTag) {
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Missing afs tag to revalidate',
    })
  }

  // Resolve domain tag to corresponding Vercel deployment URL
  // This maps internal domain identifiers to actual deployment URLs
  const domainVercelUrl = getVercelUrl(domainTag)

  if (domainVercelUrl) {
    // Convert AFS tag to corresponding page slugs for the specific domain
    // Different domains may have different AFS page structures
    const afsSlug = getAFSslugFromTag(afsTag, domainTag)

    if (afsSlug?.length > 0) {
      // Make cross-domain API call to target domain's revalidation endpoint
      // This triggers cache invalidation on the appropriate domain deployment
      const revalidateResponse = await fetch(
        `${domainVercelUrl}/api/revalidate/page/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ slugs: afsSlug }),
        }
      ).then((res) => res.json())

      // Forward the revalidation response from target domain
      return Response.json(revalidateResponse)
    } else {
      // Return error when AFS tag cannot be mapped to slugs
      return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'AFS tag not found or not supported',
      })
    }
  } else {
    // Return error when domain tag cannot be resolved to URL
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Domain not found or not supported',
    })
  }
}

function getAFSslugFromTag(afsTag: string, domainTag: string): string[] {
  switch (domainTag) {
    case 'domainAltusGroupCom':
      const tagToslug: Record<string, string[]> = {
        afsInsights: [
          '/insights/all',
          '/insights/all/',
          '/insights/all/fr',
          '/insights/all/fr/',
        ],
        afsPressRelease: [
          '/press-releases',
          '/press-releases/',
          '/press-releases/fr',
          '/press-releases/fr/',
        ],
        afsEvents: ['/events', '/events/', '/events/fr', '/events/fr/'],
      }
      return tagToslug[afsTag]
    case 'domainReonomyCom':
      return []
    case 'domainFinanceActiveCom':
      return [
        '/resources',
        '/resources/',
        '/fr/ressources',
        '/fr/ressources/',
        '/de/ressourcen',
        '/de/ressourcen/',
        '/it/risorse',
        '/it/risorse/',
        '/es/recursos',
        '/es/recursos/',
        '/nl/media',
        '/nl/media/',
      ]
    default:
      return []
  }
}

function getVercelUrl(domainTag: string) {
  const domainToUrl: Record<string, string> = {
    domainAltusGroupCom: 'https://msa-agl-v3w-git-staging-altus.vercel.app',
    domainReonomyCom: 'https://msa-reo-v3w-git-staging-altus.vercel.app',
    domainFinanceActiveCom: 'https://msa-fia-v3w-git-staging-altus.vercel.app',
  }
  return domainToUrl[domainTag]
}
