'use client'
import React, { useEffect, useState } from 'react'
import SimpleSpan from '../../ContentBlocks/Texts/Spans/SimpleSpan'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import SimpleControl from '../SimpleControl'
import { CheckboxD } from './defaults'
import styles from './index.module.scss'
import { CheckboxI } from './interface'

export default function Checkbox(props: CheckboxI): React.ReactElement {
  const updatedProps: CheckboxI = {
    ...CheckboxD,
    ...props,
    htmlAttr: {
      ...props?.htmlAttr,
    },
  }

  const [isChecked, setIsChecked] = useState<boolean>(
    updatedProps.isChecked ?? false
  )

  useEffect(() => {
    setIsChecked(updatedProps.isChecked!)
  }, [updatedProps.isChecked])

  const handleCheckboxChange = () => {
    setIsChecked((prev) => !prev)
    updatedProps.onChange && updatedProps.onChange(!isChecked)
  }
  let iconStyle = styles.icon
  if (props.isInvalid) {
    iconStyle += ` ${styles.inValidIcon}`
  }
  const icon = isChecked ? (
    <GenericIcon
      as='span'
      icon='BsFillCheckSquareFill'
      size={'md'}
      htmlAttr={{ className: !props.isLightMode ? iconStyle : styles.iconDark }} // Add a separate class for the icon
    />
  ) : (
    <GenericIcon
      as='span'
      icon='RiCheckboxBlankLine'
      size={'md'}
      htmlAttr={{ className: iconStyle }} // Add a separate class for the icon
    />
  )

  return (
    <SimpleControl {...updatedProps} isAsIs>
      <label
        className={`${styles.checkboxRoot} ${!props.isLightMode ? '' : styles.darkCheckboxRoot} ${isChecked ? styles.checked : ''} ${updatedProps?.htmlAttr?.className}`}
        onClick={(e) => {
          e.stopPropagation()
          handleCheckboxChange()
        }}
      >
        {icon}
        <SimpleSpan
          {...updatedProps.label}
          htmlAttr={{
            className: `${styles.label} ${
              isChecked ? styles.checkedLabel : ''
            }`,
          }}
        />
      </label>
    </SimpleControl>
  )
}
