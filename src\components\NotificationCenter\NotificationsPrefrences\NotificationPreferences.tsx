'use client'
import { LoadingOutlined } from '@ant-design/icons'
import { Spin } from 'antd'
import { AnimatePresence, motion } from 'framer-motion'
import Checkbox from '../../Controls/Checkbox'
import SimpleButton from '../../CTAs/SimpleButton'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import styles from '../index.module.scss'
import {
  AllowedNotificationType,
  history,
  KeepNotificationInterval,
} from '../utils'

interface Props {
  // showSettingsPanel: boolean
  isPrefSaved: boolean
  handleNotificationPreferences: ({
    category,
    value,
  }: {
    category: string
    value: string | boolean
  }) => void
  preferences: {
    allowedNotificationTypes: string[]
    clearAfter: string
    noExperience: string
    noBrowserNoti: string
    experience: string
    isSoundEnabled: string
  }
  isPermissionAllowed: boolean
  handleAllowNotifications: () => void
  isNotiPermissionLoading: boolean
}

function NotificationPreferences({
  handleNotificationPreferences,
  preferences,
  isPrefSaved,
  isPermissionAllowed,
  handleAllowNotifications,
  isNotiPermissionLoading,
}: Props) {
  return (
    <div
      style={{
        width: '100%',
        padding: '20px',
        overflowY: isPermissionAllowed ? 'auto' : 'hidden',
      }}
      className={styles.bgGradient}
    >
      <AnimatePresence mode='wait'>
        {/* when user has not allowed notification, show button to allow */}
        {!isPermissionAllowed && (
          <>
            <motion.div
              key='permissionNotice'
              initial={{ opacity: 1, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              style={
                {
                  // padding: '20px',
                }
              }
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className={styles.noNotificationWrapper}
            >
              <h5 className={styles.noNotificationHeading}>
                Allow personalised notifications from Altus Group and Stay in
                the know.
              </h5>
              {!isNotiPermissionLoading ? (
                <SimpleButton
                  isButton={true}
                  textContent={'Click to enable enhanced experience'}
                  variant={'primary'}
                  type={'SimpleButton'}
                  isLightMode={false}
                  htmlAttr={{
                    className: styles.enhancedButton,
                    onClick: (e: any) => handleAllowNotifications(),
                  }}
                />
              ) : (
                <div style={{ height: '50px' }}>
                  <Spin
                    indicator={
                      <LoadingOutlined spin style={{ fontSize: 24 }} />
                    }
                    size='large'
                  />
                </div>
              )}
            </motion.div>

            <motion.div
              key='blackBackdrop'
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.85 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4, ease: 'easeOut' }}
              className={styles.blackOverlay}
            />

            <motion.div
              key='invisibleSpacer'
              initial={{ opacity: 1, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className={styles.invisible}
            />
          </>
        )}
      </AnimatePresence>

      <div
        className={styles.settingsContent}
        style={
          {
            // padding: '20px',
          }
        }
      >
        <div className={styles.checkboxSection}>
          <div className={styles.settingsHeadingRow}>
            <div className={styles.personaliseHeading}>
              <div className={styles.iconWrap}>
                <AnimatePresence mode='wait'>
                  {!isPrefSaved ? (
                    <motion.div
                      key='person'
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2, ease: 'easeOut' }}
                      className={styles.iconInner}
                    >
                      <GenericIcon
                        icon='Person'
                        iconColour='cs2'
                        size='md'
                        htmlAttr={{
                          'data-tooltip-id': 'tooltip',
                          className: styles.personaliseIcon,
                        }}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key='tick'
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ duration: 0.2, ease: 'easeOut' }}
                      className={styles.iconInner}
                    >
                      <GenericIcon
                        icon='Tickmark'
                        iconColour='cp1'
                        bgColour='bs1'
                        isRounded
                        size='sm'
                        htmlAttr={{
                          'data-tooltip-id': 'tooltip',
                          className: styles.tickIcon,
                        }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              <h5 className={styles.settingsHeading}>
                Personalise your Altus Experience
              </h5>
            </div>
            <hr />
          </div>
          <div>
            <div className={styles.headingWIcon}>
              <GenericIcon
                icon={'Bell'}
                iconColour={'cs2'}
                isRounded
                size={'sm'}
                htmlAttr={{
                  'data-tooltip-id': 'tooltip',
                  className: styles.headingIcon,
                }}
              />
              <p className={styles.subheading}>Interests</p>
            </div>
            <label className={styles.settingsLabel}>
              Select the categories you want to be notified about
            </label>
            <div className={styles.checkboxRoot}>
              <label className={styles.settingsInnerHeading}>
                <Checkbox
                  isLightMode={true}
                  label={{ textContent: 'All Altus Group updates' }}
                  isChecked={preferences?.allowedNotificationTypes?.includes(
                    'general'
                  )}
                  onChange={() =>
                    handleNotificationPreferences({
                      category: 'allowedNotificationTypes',
                      value: 'general',
                    })
                  }
                />
              </label>
              <div className={styles.checkboxColumns}>
                {AllowedNotificationType?.map((item) => (
                  <div className={styles.checkboxColumn} key={item?.id}>
                    <Checkbox
                      isLightMode={true}
                      label={{ textContent: item.title }}
                      isChecked={preferences?.allowedNotificationTypes?.includes(
                        item?.value
                      )}
                      onChange={() => {
                        handleNotificationPreferences({
                          category: 'allowedNotificationTypes',
                          value: item?.value,
                        })
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <hr />
        <div className={`${styles.dropdownRoot}`}>
          <div className={styles.headingWIcon}>
            <GenericIcon
              icon={'lightening'}
              iconColour={'cs2'}
              isRounded
              size={'sm'}
              htmlAttr={{
                'data-tooltip-id': 'tooltip',
                className: styles.headingIcon,
              }}
            />
            <p className={styles.subheading}>Experience</p>
          </div>
          <p className={styles.settingsLabel}>
            Would you like to enhance your experience with occasional reminders?
          </p>
          <div className={`${styles.select}`}>
            <select
              value={preferences?.experience}
              onChange={(event) => {
                handleNotificationPreferences({
                  category: 'experience',
                  value: event?.target?.value,
                })
              }}
              className={`${styles.dropdownDiv} ${styles.formControl}`}
              name={'experience'}
              required={true}
              id={'experience'}
            >
              {history?.map((item) => (
                <option value={item?.value} key={item?.id}>
                  {item?.title}
                </option>
              ))}
            </select>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='16'
              height='16'
              fill='currentColor'
              className='bi bi-chevron-down'
              viewBox='0 0 16 16'
            >
              <path
                fillRule='evenodd'
                d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708'
              />
            </svg>
          </div>
        </div>
        <hr />
        <div className={`${styles.dropdownRoot}`}>
          <div className={styles.headingWIcon}>
            <GenericIcon
              icon={'ClockHistory'}
              iconColour={'cs2'}
              isRounded
              size={'sm'}
              htmlAttr={{
                'data-tooltip-id': 'tooltip',
                className: styles.headingIcon,
              }}
            />
            <p className={styles.subheading}>History</p>
          </div>
          <p className={styles.settingsLabel}>
            How long do you want to keep your notifications history in this
            browser?
          </p>
          <div className={`${styles.select}`}>
            <select
              value={preferences?.clearAfter}
              onChange={(event) => {
                handleNotificationPreferences({
                  category: 'clearAfter',
                  value: event?.target?.value,
                })
              }}
              className={`${styles.dropdownDiv} ${styles.formControl}`}
              name={'clearAfter'}
              required={true}
              id={'clearAfter'}
            >
              {KeepNotificationInterval?.map((item) => (
                <option value={item?.value} key={item?.id}>
                  {item?.title}
                </option>
              ))}
            </select>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='16'
              height='16'
              fill='currentColor'
              className='bi bi-chevron-down'
              viewBox='0 0 16 16'
            >
              <path
                fillRule='evenodd'
                d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708'
              />
            </svg>
          </div>
        </div>
        <hr />
        <Checkbox
          isLightMode={true}
          label={{ textContent: 'Enable Audible Hints' }}
          isChecked={preferences?.isSoundEnabled === 'true'}
          onChange={() =>
            handleNotificationPreferences({
              category: 'isSoundEnabled',
              value: preferences?.isSoundEnabled === 'false' ? 'true' : 'false',
            })
          }
        />
        <hr />
        <Checkbox
          isLightMode={true}
          label={{ textContent: 'Do not show Altus Group Updates' }}
          isChecked={preferences?.noExperience === 'true'}
          onChange={() =>
            handleNotificationPreferences({
              category: 'noExperience',
              value: preferences?.noExperience === 'false' ? 'true' : 'false',
            })
          }
        />
        <hr />
        <Checkbox
          isLightMode={true}
          label={{ textContent: 'Do not show Browser Notifications' }}
          isChecked={preferences?.noBrowserNoti === 'true'}
          onChange={() =>
            handleNotificationPreferences({
              category: 'noBrowserNoti',
              value: preferences?.noBrowserNoti === 'false' ? 'true' : 'false',
            })
          }
        />
      </div>
    </div>
  )
}

export default NotificationPreferences