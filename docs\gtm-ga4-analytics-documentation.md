# GTM and GA4 Analytics Implementation Documentation

## Overview

This document provides a comprehensive overview of the Google Tag Manager (GTM) and Google Analytics 4 (GA4) implementation in the 17.studio Next.js project. The system includes multi-domain analytics tracking, custom event management, conversion tracking, and integration with various third-party platforms including Microsoft Bing UET and Salesforce Marketing Cloud.

## Architecture

### Core Analytics Components

1. **Google Tag Manager (GTM)** - Centralized tag management and deployment
2. **Google Analytics 4 (GA4)** - Advanced analytics and measurement
3. **Microsoft Bing UET** - Conversion tracking for Microsoft Ads
4. **Salesforce Marketing Cloud** - Email marketing analytics
5. **Custom Event Tracking** - Business-specific analytics events
6. **Measurement Protocol** - Server-side event tracking

## Environment Configuration

### Required Environment Variables

```env
# GTM Configuration
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX

# GA4 Configuration
NEXT_PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX
GA4_API_SECRET=your_ga4_api_secret

# Firebase Analytics (Alternative GA4 Setup)
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-GVPQRF3CYC
FIREBASE_API_SECRET=gwuD3wpPQ2m-GqYtDk6zUA

# Google Maps (for location tracking)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_maps_api_key
```

### Domain-Specific Configuration

**Location**: `src/globals/utils.tsx`

```typescript
export const BUISNESS_DOMAINS = {
  altus: 'domainAltusGroupCom',
  finance: 'domainFinanceActiveCom',
  verifino: 'domainVerifinoCom',
  one11com: 'domainOne11Com',
  reonomycom: 'domainReonomyCom',
  agStudio: 'domainAgStudio',
}

export const TAG_TO_URL = {
  domainAltusGroupCom: 'https://www.altusgroup.com',
  domainVerifinoCom: 'https://www.verifino.com',
  domainFinanceActiveCom: 'https://financeactive.com',
  domainReonomyCom: 'https://www.reonomy.com',
  domainOne11Com: 'https://www.one11advisors.com',
  domainForburyCom: 'https://www.forbury.com',
  domainAgStudio: 'https://ag.studio',
}
```

## GTM Implementation

### Primary GTM Setup

**Location**: `src/app/layout.tsx`

```typescript
const gtmId = process.env.NEXT_PUBLIC_GTM_ID

const TheAnalytics = vercelEnvData && vercelEnvData?.branch === 'main' ? (
  <>
    {gtmId && (
      <script
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});let f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${gtmId}');`,
        }}
      ></script>
    )}
  </>
) : vercelEnvData.branch === "staging" ? (
  // Staging GTM implementation
) : (
  <></>
)
```

### Environment-Based Loading

- **Production (`main` branch)**: Full GTM + Bing UET + SFMC tracking
- **Staging**: GTM only for testing
- **Development**: No analytics loading

### NoScript Fallback

```typescript
<noscript
  dangerouslySetInnerHTML={{
    __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${gtmId}"
             height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
  }}
></noscript>
```

## GA4 Implementation

### DataLayer Event Tracking

**Location**: `src/utils/analyticsEvents.ts`

#### Core Event Functions

```typescript
// Form Events
export function formSuccessEvent({
  contentfulId,
  formCategory,
  contactInquiryType = null,
  productInterest = null,
}) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_success',
      formId: contentfulId,
      formCategory,
      contactInquiryType,
      productInterest,
    })
  }
}

export function formErrorEvent({ contentfulId, formCategory }) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_error',
      formId: contentfulId,
      formCategory,
    })
  }
}

// Search Events
export function searchOpenEvent() {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'open_search_screen',
    })
  }
}

export function enteredSearchInputEvent() {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'entered_search_input',
    })
  }
}

export function searchResultClickEvent(href?: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'clicked_search_result',
      clicked_url: href,
    })
  }
}
```

#### Advanced Filtering System (AFS) Events

```typescript
export function copyFilterEvent(copiedUrl: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'afs_copy_filter_url',
      copied_url: copiedUrl,
    })
  }
}

export function clearAllFilterEvent() {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'afs_clear_all_filter',
    })
  }
}

export function sortByEvent(option: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'afs_sort_by',
      selected_option: option,
    })
  }
}

export function afsMenuEvent(category: string, option: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'afs_menu',
      category: category,
      selected_option: option,
    })
  }
}
```

#### CTA Intent Tracking

```typescript
export const CTA_INTENTS = {
  OPEN_POPUP: 'OPEN_POPUP',
  GET_INFO: 'GET_INFO', // contact / mail / phone
  GO_TO: 'GO_TO',
  UNCLASSIFIED: 'UNCLASSIFIED',
}

export function ctaClickEvent({
  ctaContent,
  intent,
  intentObjectInfo,
}: ICtaClickEventProps) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'event_click',
      intent: intent,
      intent_object_info: intentObjectInfo,
      cta_content: ctaContent, // icon_{icon name} || button text
    })
  }
}
```

### Client ID and Measurement Utilities

```typescript
export function getMeasurementId() {
  const scripts = Array.from(document?.scripts || [])
  for (const script of scripts) {
    const src = script?.src || ''
    const match = src?.match(
      /https:\/\/www.googletagmanager.com\/gtag\/js\?id=(G-[A-Z0-9]+)/
    )
    if (match?.[1]) return match[1]
  }
  return null
}

export function getClientIdFromCookie() {
  const gaCookie = getClientSideCookie('_ga')

  if (!gaCookie) return null

  const parts = gaCookie?.split('.')
  if (parts?.length >= 4) {
    return `${parts[2]}.${parts[3]}` // This is the client_id
  }

  return null
}
```

## Server-Side Analytics

### Measurement Protocol Implementation

**Endpoint**: `POST /api/send-notification-analytics`
**Location**: `src/app/api/send-notification-analytics/route.ts`

```typescript
const analyticsConfig = {
  apiSecret: ENV_VARS.FIREBASE_API_SECRET,
  measurementId: ENV_VARS.FIREBASE_MEASUREMENT_ID,
}

export async function POST(req: Request) {
  const body = await req.json()

  const analyticsUrl = `https://www.google-analytics.com/mp/collect?measurement_id=${analyticsConfig.measurementId}&api_secret=${analyticsConfig.apiSecret}`

  try {
    const res = await fetch(analyticsUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    })

    if (!res.ok) {
      const text = await res.text()
      console.error('GA4 error response:', text)
      return new Response(JSON.stringify({ error: text }), {
        status: res.status,
      })
    }
  } catch (error: any) {
    console.error('Error sending analytics:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
    })
  }

  return new Response(JSON.stringify({ message: 'analytics sent' }), {
    status: 200,
  })
}
```

### Page Publish Event Tracking

**Endpoint**: `POST /api/page-publish-event`
**Location**: `src/app/api/page-publish-event/route.ts`

```typescript
// Send event to GA4 using Measurement Protocol
const response = await fetch(
  `https://www.google-analytics.com/mp/collect?api_secret=${process.env.GA4_API_SECRET}&measurement_id=${process.env.NEXT_PUBLIC_GA4_MEASUREMENT_ID}`,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      client_id: 'contentful-webhook', // Arbitrary ID
      events: [
        {
          name: 'page_publish',
          params: eventObj,
        },
      ],
    }),
  }
)
```

## Third-Party Integrations

### Microsoft Bing UET (Universal Event Tracking)

**Location**: `src/app/layout.tsx`

```typescript
<script
  dangerouslySetInnerHTML={{
    __html: ` (function(w,d,t,r,u)
    {
      let f,n,i;
      w[u]=w[u]||[],f=function()
      {
        let o={ti:"97098596", enableAutoSpaTracking: true};
        o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")
      },
      n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
      {
        let s=this.readyState;
        s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)
      },
      i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
    })
    (window,document,"script","//bat.bing.com/bat.js","uetq");`,
  }}
></script>
```

#### UET Conversion Tracking

**Location**: `src/systems/AltusForms/Form/utils.tsx`

```typescript
export function uet_report_conversion() {
  if (isBrowser()) {
    window.uetq = window?.uetq || []
    window.uetq.push('event', 'submit_lead_form', {})
  }
}
```

### Salesforce Marketing Cloud (SFMC)

**Location**: `src/providers/FormDataProvder.tsx`

```typescript
const domain: string = process.env.NEXT_PUBLIC_DOMAIN

if (
  isProduction() &&
  (domain === 'domainAltusGroupCom' || domain === 'domainReonomyCom')
) {
  let sfmc_sub = getParameterByName('sfmc_sub')
  window._etmc?.push(['setOrgId', '546007750'])
  if (sfmc_sub != '') {
    window._etmc?.push(['setUserInfo', { email: sfmc_sub }])
    console.log('SMFC_SUB=' + sfmc_sub)
  }
  let url = location.protocol + '//' + location.host + location.pathname
  console.log('URL=' + url)
  window._etmc?.push(['trackPageView', { item: url }])
}
```

#### SFMC Email Tracking

```typescript
function emailTracking(email: string) {
  if (!email || !isProduction()) return
  if (isBrowser()) {
    console.log('Email=' + email)
    window._etmc.push(['setOrgId', '546007750'])
    window._etmc.push(['setUserInfo', { email: email }])
    window._etmc.push(['trackPageView'])
  }
}
```

## Form Analytics Integration

### Form Lifecycle Tracking

**Location**: `src/systems/AltusForms/Form/utils.tsx`

```typescript
// Form Start Tracking
export function formStartTracking(formId: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_start',
      formId: formId,
    })
  }
}

// Form Close Tracking
export function GA4FormCloseTracking(formId: string) {
  if (isBrowser()) {
    window.dataLayer?.push({
      event: 'custom_form_floating_close',
      formId: formId,
    })
  }
}
```

### Form Integration with Campaign Tracking

```typescript
export const checkAndAddSFIDToFormProps = (fieldValues: any) => {
  let params = getQueryParamJson()
  const sfidParams = params?.['sfid']

  for (const fieldValue in fieldValues) {
    if (fieldValue === 'Integration_Primary_Campaign' && sfidParams) {
      fieldValues['Integration_Primary_Campaign'] = sfidParams
    }
  }

  return fieldValues
}
```

### UTM Parameter Handling

**Location**: `src/globals/utils.tsx`

```typescript
export const EXCLUDED_KEYS = [
  'lang',
  'variant',
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'gclid',
  'fbclid',
  'msclkid',
]
```

## Push Notifications Analytics

### Notification Event Tracking

**Location**: `src/components/PushNotifications/index.tsx`

```typescript
async function sendNotificationAnalytics(
  eventType: 'click' | 'close',
  notification: any
) {
  let eventString = {
    click: 'click',
    close: 'close',
  }

  let payload = {
    client_id: getCookieValue('_ga') || generateUniqueId(),
    events: [
      {
        name: `notification_${eventString[eventType]}`,
        params: {
          title: notification?.title,
          description: notification?.body,
          type:
            notification?.type === 'both' ? 'Page & Browser' : 'Only Browser',
          url: notification?.data?.url || '',
        },
      },
    ],
  }

  try {
    await fetch('/api/send-notification-analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })
  } catch (error) {
    console.error('Error sending analytics:', error)
  }
}
```

## Search Analytics Integration

### Search Input Tracking

**Location**: `src/components/Controls/SearchInput/index.tsx`

```typescript
const [isGA4Tracked, setIsGA4Tracked] = useState<boolean>(false)

const inputChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
  setInput(e.target.value)
  setTimeout(() => {
    refine(e.target.value)
    if (e.target.value.length > 0) {
      dispatch(setRePopulateReducer(true))
    } else {
      dispatch(setRePopulateReducer(false))
    }
  }, 1000)

  if (!isGA4Tracked) {
    enteredSearchInputEvent()
    setIsGA4Tracked(true)
  }
}
```

## Structured Data and SEO

### Company Schema Markup

**Location**: `src/app/layout.tsx`

```typescript
const companySeoJson = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Altus Group',
  url: 'https://www.altusgroup.com',
  logo: 'https://www.altusgroup.com/logo/altus/english/Altus-Logo.svg',
  description: 'Altus Group provides the global CRE industry with asset intelligence...',
  email: '<EMAIL>',
  telephone: '(*************',
  address: {
    '@type': 'PostalAddress',
    streetAddress: '33 Yonge Street, Suite #500',
    addressLocality: 'Toronto',
    addressCountry: 'CA',
    addressRegion: 'Ontario',
    postalCode: 'M5E 1G4',
  },
  founder: 'Not applicable',
  foundingDate: '2005',
}

// Domain-specific schema injection
{domain === 'domainAltusGroupCom' && (
  <script
    type='application/ld+json'
    dangerouslySetInnerHTML={{
      __html: JSON.stringify(companySeoJson),
    }}
  ></script>
)}
```

## Performance Monitoring

### Vercel Speed Insights Integration

```typescript
import { SpeedInsights } from '@vercel/speed-insights/next'

// Only load in production
{vercelEnvData?.branch === 'main' && <SpeedInsights />}
```

## Data Privacy and Compliance

### Cookie Management

```typescript
export const getClientSideCookie = (name: string): string | undefined => {
  const cookieValue = document?.cookie
    ?.split('; ')
    ?.find((row) => row?.startsWith(`${name}=`))
    ?.split('=')?.[1]

  return cookieValue
}
```

### Browser Detection and Safety

```typescript
import { isBrowser } from '../globals/utils'

// All analytics functions check browser environment
if (isBrowser()) {
  window.dataLayer?.push({
    event: 'custom_event',
    // event data
  })
}
```

## Error Handling and Debugging

### Analytics Error Logging

```typescript
// Comprehensive error handling in API routes
try {
  const res = await fetch(analyticsUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body),
  })

  console.log('GA4 response status:', res.status, 'OK?', res.ok)

  if (!res.ok) {
    const text = await res.text()
    console.error('GA4 error response:', text)
    return new Response(JSON.stringify({ error: text }), {
      status: res.status,
    })
  }
} catch (error: any) {
  console.error('Error sending analytics:', error)
  return new Response(JSON.stringify({ error: error.message }), {
    status: 500,
  })
}
```

### Development vs Production Logging

```typescript
// Conditional logging based on environment
if (process.env.NEXT_PUBLIC_LOG === 'true') {
  console.log(...args, '\n\n++++++++++++ CL Function Log ++++++++++++++++')
}
```

## Best Practices and Recommendations

### 1. Event Naming Convention

- Use descriptive, consistent event names: `custom_form_success`, `afs_copy_filter_url`
- Include relevant context in event parameters
- Maintain backwards compatibility when updating events

### 2. Performance Optimization

- Load analytics only in appropriate environments (production/staging)
- Use conditional loading based on domain requirements
- Implement proper error boundaries for analytics failures

### 3. Data Quality

- Validate event parameters before sending
- Include fallback client IDs for server-side events
- Implement retry logic for failed analytics calls

### 4. Privacy Compliance

- Respect user consent preferences
- Implement proper cookie management
- Provide clear opt-out mechanisms

### 5. Testing and Validation

- Use GTM Preview mode for testing
- Validate events in GA4 DebugView
- Implement staging environment testing

## Troubleshooting Common Issues

### 1. Events Not Firing

- **Check**: Browser environment detection (`isBrowser()`)
- **Verify**: GTM container loading and dataLayer initialization
- **Test**: Event firing in browser console

### 2. Server-Side Events Failing

- **Validate**: API secrets and measurement IDs
- **Check**: Network connectivity and CORS settings
- **Monitor**: API response status and error logs

### 3. Cross-Domain Tracking Issues

- **Review**: Domain configuration in GTM
- **Verify**: Cookie domain settings
- **Check**: Referrer policy configuration

### 4. Form Tracking Problems

- **Ensure**: Proper form ID mapping
- **Validate**: Event timing and lifecycle
- **Check**: SFMC integration for specific domains

## Maintenance and Updates

### Regular Maintenance Tasks

1. **Review event taxonomy** and consolidate similar events
2. **Monitor data quality** and fix tracking gaps
3. **Update measurement IDs** for new domains/properties
4. **Audit third-party integrations** for API changes

### Version Updates

- Test analytics behavior after Next.js updates
- Verify GTM container compatibility
- Update documentation for new tracking requirements

### Performance Audits

- Regular analysis of analytics loading impact
- Optimization of event firing frequency
- Review of server-side analytics efficiency

```

```
