import { createSlice } from '@reduxjs/toolkit'
import { PayloadAction } from '@reduxjs/toolkit/dist/createAction'
import { setRecentSearches } from '../../globals/utils'

// define initial state here
const initialState = {
  isSearchActive: false,
  recentSearch: {},
  rePopulate: false,
}

/**
 * Search Redux Slice
 *
 * Manages global search state including:
 * - Search active/inactive state
 * - Recent search tracking
 * - Search result population control
 *
 * This slice integrates with Algolia search functionality and local storage
 * to provide a seamless search experience across the application.
 */
const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    /**
     * Set search active/inactive state
     * When search becomes inactive and there's a recent search, save it to local storage
     * @param state - Current search state
     * @param action - Boolean payload indicating if search is active
     */
    setSearchState: (state, action: PayloadAction<boolean>) => {
      state.isSearchActive = action.payload

      // When search becomes inactive, save recent search to local storage
      // This ensures search history is preserved when user closes search
      if (
        action.payload === false &&
        Object.keys(state.recentSearch).length !== 0
      ) {
        setRecentSearches(state.recentSearch)
      }
    },

    /**
     * Update the recent search object in state
     * Used to track the most recent search performed by the user
     * @param state - Current search state
     * @param action - Recent search object payload
     */
    setRecentSearchReducer: (state, action: PayloadAction<any>) => {
      state.recentSearch = action.payload
    },

    /**
     * Control search result population
     * Used to manage when search results should be repopulated or refreshed
     * @param state - Current search state
     * @param action - Boolean payload for repopulation control
     */
    setRePopulateReducer: (state, action: PayloadAction<boolean>) => {
      state.rePopulate = action.payload
    },
  },
})

// Export action creators for use in components
export const { setSearchState, setRecentSearchReducer, setRePopulateReducer } =
  searchSlice.actions

// Export reducer for store configuration
export default searchSlice.reducer
