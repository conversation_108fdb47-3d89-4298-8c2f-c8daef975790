import { notification } from 'antd'
import { Provider } from 'react-redux'
import store from '../../../redux/store'
import SimpleButton from '../../CTAs/SimpleButton'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import { dismissGeoNotification } from '../../NotificationCenter/GeoLocationNotification'
import './index.scss'

export const ShowNotification = (props: NotificationBody) => {
  notification.open({
    message: props?.title,
    description: (
      <>
        <div className='ant-description-text'>{props?.body}</div>
        <div className={'btnGroup'}>
          <Provider store={store}>
            <SimpleButton
              variant={'tertiary3'}
              // href={props?.url || ''}
              textContent={'Close'}
              htmlAttr={{
                className: 'link1',
                onClick: (e: any) => {
                  e.stopPropagation()
                  e.preventDefault()
                  props?.handleClose && props.handleClose()
                  notification.destroy()
                  dismissGeoNotification()
                },
              }}
            />
          </Provider>
          <Provider store={store}>
            <SimpleButtonWIcon
              isChevron2Arrow
              variant={'tertiary'}
              // href={props?.url || ''}
              textContent={'Read more'}
              htmlAttr={{
                className: 'link2',
                onClick: (e: any) => {
                  e.stopPropagation()
                  e.preventDefault()
                  window.open(props?.url || '', '_self')
                },
              }}
            />
          </Provider>
        </div>
      </>
    ),
    icon: (
      <img
        src={props?.icon || ''}
        alt='notification-icon'
        style={{
          width: 60,
          height: 60,
          marginRight: 10,
          border: '1px solid #E5E5E5',
        }}
        className={'rounded max shadow3'}
      />
    ),
    onClick: () => {
      props?.handleClick && props.handleClick()
      notification.destroy()
    },
    onClose: () => {
      props?.handleClose && props.handleClose()
    },
    closeIcon: null,
    style: { width: 400, overflow: 'visible' },
    duration: props?.isPersistent ? null : Number(props?.duration) || 10,
    showProgress: true,
  })
}

export interface NotificationBody {
  title?: string
  body?: string
  url?: string
  icon?: string
  handleClose?: () => void
  duration?: string
  isPersistent?: boolean
  handleClick?: () => void
}
