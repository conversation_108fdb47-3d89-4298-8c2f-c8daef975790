export const UID_COOKIE = 'uid'
export const EVEN_ODD = 'evenoddgroup'
export const USER_EVN_ODD_ID = 'userevenoddid'
export const IS_UUID = /^[0-9a-f-]+$/i
export const EXPERIMENT_TYPE = {
  STATSIG: 'Statsig',
  EVEN_ODD: 'EvenOdd',
}

export const MCA_LINKS = {
  USA: '/downloads/legal/agreements/master-customer-agreement-us.pdf',
  UK: '/downloads/legal/agreements/master-customer-agreement-uk.pdf',
  AU: '/downloads/legal/agreements/master-customer-agreement-aus.pdf',
  FORBURY: '/downloads/legal/Forbury/ForburyAddendum.pdf'
}

export const GEO_NOTIFICATION_SLUGS = [
  'solutions/au',
  'solutions/uk',
  'solutions/us',
]