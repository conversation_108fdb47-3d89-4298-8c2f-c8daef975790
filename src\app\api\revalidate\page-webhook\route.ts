/**
 * Webhook-Based Page Revalidation API Route
 *
 * This endpoint is specifically designed for Contentful webhook integration,
 * handling automatic cache invalidation when content is published or updated.
 * It processes Contentful's webhook payload structure and extracts slug information
 * for targeted page revalidation.
 *
 * Contentful Integration:
 * - Processes webhook payloads from Contentful CMS
 * - Extracts slug data from nested fields structure
 * - Handles multiple slug formats and localization
 * - Supports both trailing slash and non-trailing slash URLs
 *
 * Expected Webhook Payload:
 * {
 *   fields: {
 *     slug: {
 *       "en-US": "about-us",
 *       "es": "acerca-de"
 *     }
 *   }
 * }
 *
 * URL Generation:
 * - Creates both /slug and /slug/ variants for comprehensive cache clearing
 * - Handles localized slugs for multi-language content
 * - Ensures all possible URL variations are revalidated
 */

import { revalidatePath } from 'next/cache'
import { NextRequest } from 'next/server'

/**
 * Handle POST request from Contentful webhooks for page revalidation
 * Processes Contentful's nested field structure and extracts slug information
 *
 * Expected JSON structure in the request body:
 * {
 *   fields: {
 *     slug: {
 *       [locale: string]: string  // e.g., "en-US": "about-us"
 *     }
 *   }
 * }
 *
 * @param request - Next.js request object containing Contentful webhook payload
 * @returns JSON response with revalidation status and processed slugs
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const slugObject = body?.fields?.slug

    // Validate slug object structure from Contentful webhook
    if (!slugObject || typeof slugObject !== 'object') {
      return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'Invalid or missing slug data',
        requestBody: body,
      })
    }

    // Extract slug values from all locales and build possible URL formats
    // This handles both localized and non-localized content
    const slugValues: string[] = Object.values(slugObject)
    const allSlugs = slugValues.flatMap((slug) => [`/${slug}/`, `/${slug}`])

    if (allSlugs.length === 0) {
      return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'No slugs provided to revalidate',
      })
    }

    // Revalidate each path variant to ensure comprehensive cache clearing
    // This covers all possible URL formats that might be cached
    for (const path of allSlugs) {
      revalidatePath(path)
    }

    return Response.json({
      revalidated: true,
      now: Date.now(),
      slugs: allSlugs,
    })
  } catch (error) {
    console.error('Error during revalidation:', error)
    return Response.json({
      revalidated: false,
      now: Date.now(),
      error: 'Failed to parse request or revalidate paths',
      requestBody: request?.body,
    })
  }
}
