import { Next<PERSON>etch<PERSON>vent, NextRequest, NextResponse } from 'next/server'
import <PERSON>atsig from 'statsig-node'
import {
  CONTINENT_REGION_MAP,
  corsAllowedOrigins,
  corsAllowedPattern,
} from '../globals/utils'
import { BUISNESS_DOMAINS } from '../utils'
import { fetchGraphQL } from './api'
import { IS_UUID, UID_COOKIE } from './constant'
import { getConfigrationwithTypeandScopr } from './queries/page.query'

export const getUserIDforExperiment = (request: NextRequest) => {
  let userId = request.cookies.get(UID_COOKIE)?.value
  let hasUserId = !!userId

  // If there's no active user ID in cookies or its value is invalid, get a new one
  if (!userId || !IS_UUID.test(userId)) {
    userId = crypto.randomUUID()
    hasUserId = false
  }
  return {
    userId,
    hasUserId,
  }
}

// // not used this was for even odd configuration
// export const getUserEvenOddIDforExperiment = (
//   request: NextRequest,
//   globalUserCount: number,
//   countFunction: Function
// ) => {
//   let UserEvenOddId = +(request?.cookies?.get(USER_EVN_ODD_ID)?.value ?? 0)
//   let EvenOddGroup = request.cookies.get(EVEN_ODD)?.value
//   let hasUserEvenOddId = !!UserEvenOddId

//   if (UserEvenOddId && +UserEvenOddId + 1 > globalUserCount) {
//     countFunction(+UserEvenOddId + 1)
//   }
//   // If there's no active user ID in cookies or its value is invalid, get a new one
//   if (!UserEvenOddId && EvenOddGroup !== 'even' && EvenOddGroup !== 'odd') {
//     UserEvenOddId = globalUserCount
//     hasUserEvenOddId = false
//     EvenOddGroup = +UserEvenOddId % 2 === 0 ? 'even' : 'odd'
//     countFunction(+UserEvenOddId + 1)
//   }

//   return {
//     UserEvenOddId,
//     hasUserEvenOddId,
//     EvenOddGroup,
//   }
// }

// variation based ?variant={anything} handeled by this function
export async function handelExperimentbasedonVariantParam({
  req,
  userId,
  hasUserId,
  // EvenOddGroup,
  // hasUserEvenOddId,
  // UserEvenOddId,
  lang,
  variantPage,
}: {
  req: NextRequest
  userId: string
  hasUserId: boolean
  // EvenOddGroup?: string
  // hasUserEvenOddId?: boolean
  // UserEvenOddId?: string | number
  lang?: string
  variantPage?: string
}) {
  // const { pathname } = req.nextUrl
  const origin = req.headers.get('origin') ?? ''

  const bucket = variantPage
  const url = req.nextUrl.clone()
  let newurl = bucket
  // Altus only language based ?lang={fr or en } handeled
  if (lang && BUISNESS_DOMAINS['altus'] == process.env.NEXT_PUBLIC_DOMAIN) {
    newurl = `${bucket}${lang}`
  }
  url.pathname = `${newurl}`

  // Response that'll rewrite to the selected bucket
  const res = NextResponse.rewrite(url)
  CookieSetTopagesforExperimentation({
    req,
    response: res,
    hasUserId,
    userId,
    // EvenOddGroup,
    // hasUserEvenOddId,
    // UserEvenOddId,
    origin,
  })

  return res
}

// first check if the page has experimentationId and then check if the user is in the experiment
export async function handelExperimentStatsigViaRedirection({
  req,
  event,
  userId,
  hasUserId,
  Paths,
  // EvenOddGroup,
  // hasUserEvenOddId,
  // UserEvenOddId,
}: {
  req: NextRequest
  event: NextFetchEvent
  userId: string
  hasUserId: boolean
  // EvenOddGroup?: string
  // hasUserEvenOddId?: boolean
  // UserEvenOddId?: string | number
  Paths: Record<string, PathData>
}) {
  const { pathname } = req.nextUrl
  const origin = req.headers.get('origin') ?? ''

  await Statsig.initialize(
    process.env.NEXT_PUBLIC_STATSIG_SERVER_KEY!
    // { environment: { tier: 'staging' } } // optional, pass options here if needed
  )

  const config = await Statsig.getExperiment(
    { userID: userId },
    Paths?.[pathname]?.experimentationId ?? 'no-exp'
  )
  const bucket = config.get('page', '/not-found')
  const url = req.nextUrl.clone()
  // let newurl = bucket
  // if (lang && BUISNESS_DOMAINS['altus'] == process.env.NEXT_PUBLIC_DOMAIN) {
  //   newurl = `${bucket}/${lang}`
  // }
  const variantID = Paths?.[pathname]?.experimentationPages?.findIndex(
    (el) => el === `/${bucket}/`
  )
  if (variantID !== -1) {
    url.searchParams.set(
      'variant',
      `${Number(variantID) + (Paths?.[pathname]?.historyCount ?? 0)}`
    )
  }

  // Response that'll rewrite to the selected bucket
  if (bucket === '/not-found') {
    const url = req.nextUrl.clone()
    url.pathname = bucket
    const res = NextResponse.redirect(url)
    CookieSetTopagesforExperimentation({
      req,
      response: res,
      hasUserId,
      userId,
      // EvenOddGroup,
      // hasUserEvenOddId,
      // UserEvenOddId,
      origin,
    })
    event.waitUntil(Statsig.flush())
    return res
  }

  const res = NextResponse.redirect(url)
  CookieSetTopagesforExperimentation({
    req,
    response: res,
    hasUserId,
    userId,
    // EvenOddGroup,
    // hasUserEvenOddId,
    // UserEvenOddId,
    origin,
  })

  event.waitUntil(Statsig.flush())

  return res
}

// // not used this was for even odd configuration
// export async function handelExperimentEvenOdd({
//   req,
//   userId,
//   hasUserId,
//   Paths,
//   EvenOddGroup,
//   hasUserEvenOddId,
//   UserEvenOddId,
// }: {
//   req: NextRequest
//   event: NextFetchEvent
//   userId: string
//   hasUserId: boolean
//   EvenOddGroup?: string
//   hasUserEvenOddId?: boolean
//   UserEvenOddId?: string | number
//   Paths: Record<string, PathData>
// }) {
//   const { pathname } = req.nextUrl
//   const origin = req.headers.get('origin') ?? ''

//   const url = req.nextUrl.clone()
//   const assignedPage = getExperimentationPage({
//     Paths,
//     pathname,
//     UserEvenOddId,
//   })
//   url.pathname = `${assignedPage}`
//   const res = NextResponse.rewrite(url)
//   CookieSetTopagesforExperimentation({
//     response: res,
//     hasUserId,
//     userId,
//     EvenOddGroup,
//     hasUserEvenOddId,
//     UserEvenOddId,
//     origin,
//   })

//   return res
// }

export const CookieSetTopagesforExperimentation = async ({
  req,
  response,
  hasUserId,
  userId,
  origin = '',
}: CookieSetTopagesforExperimentationParams) => {
  if (!hasUserId) {
    response.cookies.set(UID_COOKIE, userId, {
      maxAge: 60 * 60 * 24,
    })
  }

  // Retrieve the continent from the Vercel geo headers
  const continent: string = req?.headers?.get('x-vercel-ip-continent') || ''
  const country = req?.headers?.get('x-vercel-ip-country') || ''
  // Determine the region based on the continent code
  const region = CONTINENT_REGION_MAP[continent] || 'AMER' // default to AMER if unmapped

  const geoLocationCookiePref = {
    path: '/',
    httpOnly: false, // must be false to access from client
    maxAge: 60 * 60 * 24 * 7, // 1 week
    sameSite: 'lax',
  }
  // Set region cookie (1 week expiry)
  response.cookies.set('user_region', region, geoLocationCookiePref)
  response.cookies.set('user_country', country, geoLocationCookiePref)
  // Set CORS headers if the origin is allowed
  if (corsAllowedPattern.test(origin) || corsAllowedOrigins.includes(origin)) {
    console.log('Setting CORS headers for origin', origin)
    response.headers.append('Access-Control-Allow-Origin', origin)
    response.headers.append(
      'Access-Control-Allow-Methods',
      'POST, GET, OPTIONS'
    )
    response.headers.append('Access-Control-Allow-Headers', '*')
  }
}

// // this was for Even Odd Page assigned to user based on UserEvenOddId
// export function getExperimentationPage({
//   Paths,
//   pathname,
//   UserEvenOddId,
// }: {
//   pathname: string
//   UserEvenOddId?: string | number
//   Paths: Record<string, PathData>
// }): string | undefined {
//   const pathData = Paths[pathname]
//   if (
//     !pathData ||
//     !pathData.experimentationPages ||
//     pathData.experimentationPages.length === 0
//   ) {
//     return '/home/'
//   }
//   const pageIndex =
//     (+(UserEvenOddId ?? 1) - 1) % pathData.experimentationPages.length
//   return pathData.experimentationPages[pageIndex]
// }

// interface For Global Configuration
interface ConfigurationItem {
  data?: {
    json?: {
      content?: {
        content?: {
          value?: string
        }[]
      }[]
    }
  }
}

interface GraphQLResponse {
  data?: {
    configurationsCollection?: {
      items?: ConfigurationItem[]
    }
  }
}

// To get Global Configuration based on Type and Scope
export async function getConfigurationByScopeAndType(
  type: string,
  scope: string
): Promise<unknown | null> {
  try {
    const rawResponse = await fetchGraphQL(
      getConfigrationwithTypeandScopr(null, type, scope),
      false,
      'experimentation'
    )
    console.log(
      rawResponse,
      'experimtnationData :: rawResponserawResponserawResponse'
    )

    const response = rawResponse as GraphQLResponse
    const items = response?.data?.configurationsCollection?.items

    if (items && items?.length > 0) {
      const data = await JSON.parse(
        items?.[0]?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
      )
      // Return the JSON data from the first matched item
      console.log('experimtnationData :: experimtnationData :: ', data)
      return data
    } else {
      return null // No matching configuration found
    }
  } catch (error) {
    console.error('Error fetching configuration by scope and type:', error)
    return null
  }
}

// helper to ensure slashes at the start and end of a string
// If the string is empty, return a single slash
// If the string is not empty, ensure it starts and ends with a slash
export function withSlashes(str?: string): string {
  const cleaned = str?.replace(/^\/+|\/+$/g, '') ?? ''
  return cleaned ? `/${cleaned}/` : '/'
}
