'use server'

import csv from 'csv-parser'
import fs from 'fs'
import path from 'path'

  /**
   * @function getRedirects
   * @description Given a filename, this function will read a CSV file
   * located in the `redirects` directory and return its contents as an array
   * of objects.
   * @param {Object} options - An object with a single key: `filename`
   * @returns {Array} The contents of the CSV file
   */
export async function getRedirects({ filename }: { filename: string }) {
  const filePath = path.join(process.cwd(), 'redirects', filename)
  const results: any[] = []

  const parseCSV = () =>
    new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (err) => reject(err))
    })

  const data = await parseCSV()

  return data
}
