/**
 * GraphQL Fetch Function with Cache Tag Support
 *
 * This function provides a centralized way to fetch data from Contentful's GraphQL API
 * with built-in cache management using Next.js 13+ App Router caching system.
 * It supports selective cache invalidation through revalidation tags.
 *
 * Cache Strategy:
 * - Uses Next.js fetch with cache tags for selective revalidation
 * - Supports both preview and production modes
 * - Enables targeted cache invalidation via revalidateTag API
 *
 * Revalidation Tags:
 * - Tags allow grouping related content for batch invalidation
 * - Common tags: 'pages', 'navigation', 'global-config', 'components'
 * - Content-specific tags: 'insights', 'case-studies', 'resources'
 *
 * Environment Support:
 * - Automatic preview mode detection from environment variables
 * - Flexible token resolution for different deployment environments
 * - Configurable Contentful space and environment targeting
 *
 * @param query - GraphQL query string to execute
 * @param preview - Whether to use preview mode (overridden by env var)
 * @param revalidateTag - Optional cache tag for selective invalidation
 * @param retries - Number of retry attempts for failed requests
 * @returns Promise resolving to GraphQL response data
 */
export async function fetchGraphQL(
  query: string,
  preview = false,
  revalidateTag?: string,
  retries = 5
): Promise<unknown> {
  // Override preview mode based on environment variable
  // This ensures consistent preview behavior across the application
  preview = process.env.NEXT_PUBLIC_PREVIEW === 'true'

  // Resolve Contentful access token from environment variables
  // Supports both server-side and client-side token configurations
  const contentfulAccessToken =
    process.env.CONTENTFUL_ACCESS_TOKEN ??
    process.env.NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN

  // Configure Next.js cache settings with optional revalidation tag
  let nextConfig = {}
  if (revalidateTag) {
    // Assign cache tag for selective revalidation via revalidateTag API
    nextConfig = { tags: [revalidateTag] }
  }

  // Execute GraphQL request to Contentful with caching configuration
  const response = await fetch(
    `https://graphql.contentful.com/content/v1/spaces/${process.env.NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${process.env.NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}`,
    {
      // cache: 'no-store', // Commented out to enable Next.js caching
      next: nextConfig, // Apply cache tags for selective revalidation
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${contentfulAccessToken}`,
      },
      body: JSON.stringify({ query }),
    }
  )

  let responseData = await response.json()

  if (responseData.errors) {
    if (responseData.errors?.[0]?.message.includes('rate limit')) {
      if (retries > 0) {
        console.error(
          'Retry counter: ',
          5 - retries,
          ' API::error',
          responseData
        )
        await new Promise((resolve) => setTimeout(resolve, 500))
        responseData = await fetchGraphQL(
          query,
          preview,
          revalidateTag,
          retries - 1
        )
      } else {
        throw new Error('API::error: retries exceeded')
      }
    }
  }

  return responseData
}

export async function getEntryDataById(entryId: string) {
  const {
    CONTENTFUL_ACCESS_TOKEN,
    NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN,
    NEXT_PUBLIC_CONTENTFUL_SPACE_ID,
    NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT,
    NODE_ENV,
  } = process.env

  const contentfulAccessToken =
    CONTENTFUL_ACCESS_TOKEN ?? NEXT_PUBLIC_CONTENTFUL_ACCESS_TOKEN
  try {
    const response = await fetch(
      `https://preview.contentful.com/spaces/${NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}/entries/${entryId}?access_token=${contentfulAccessToken}`
    )
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`)
    }
    const result: GraphQLResponse<T> = await response.json()
    if (result?.errors) {
      console.error('GraphQL errors:', result.errors)
    }
    return result
  } catch (error) {
    console.log(error)
  }
}