const fs = require('fs');
const path = require('path');

// Path to the page.tsx file
const filePath = path.join(__dirname, '../src/app/[[...slug]]/page.tsx');

// New revalidate time
const newRevalidateTime = 3600; // Set your desired revalidation time in seconds

/**
 * Automated Revalidation Time Update Script
 *
 * This Node.js script automatically updates the revalidation time constant
 * in Next.js page files. It's used for deployment automation and cache
 * management workflows to ensure consistent cache behavior across environments.
 *
 * Functionality:
 * - Reads target file and searches for revalidate export
 * - Updates the numeric value using regex pattern matching
 * - Writes the modified content back to the file
 * - Provides error handling and success feedback
 *
 * Usage in CI/CD:
 * - Deployment pipelines can adjust cache timing
 * - Environment-specific revalidation intervals
 * - Automated cache optimization based on content update frequency
 */

// Read the target file for revalidation time update
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  // Regex pattern to match Next.js revalidate export statement
  // Matches: export const revalidate = <number>
  const regex = /(export const revalidate = )(\d+)/;

  // Replace the existing revalidation time with new value
  // Preserves the export statement structure while updating the numeric value
  const updatedData = data.replace(regex, `$1${newRevalidateTime}`);

  // Write the updated data back to the file
  fs.writeFile(filePath, updatedData, 'utf8', (err) => {
    if (err) {
      console.error('Error writing file:', err);
      return;
    }

    console.log(`Revalidation time updated to ${newRevalidateTime} seconds.`);
  });
});
