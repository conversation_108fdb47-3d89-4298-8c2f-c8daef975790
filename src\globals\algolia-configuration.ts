// Algolia search and recommendation imports
import recommend from '@algolia/recommend'
import algoliasearch from 'algoliasearch'

/**
 * Algolia Configuration Module
 *
 * This module centralizes all Algolia-related configuration including:
 * - Search client initialization
 * - Recommendation client setup
 * - Index name configuration
 * - Domain-specific settings
 *
 * Environment Variables Required:
 * - NEXT_PUBLIC_ALGOLIA_INDEX: The Algolia index name for search operations
 * - NEXT_PUBLIC_ALGOLIA_APPLICATION_ID: Algolia application identifier
 * - NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY: Public API key for search operations (read-only)
 * - NEXT_PUBLIC_DOMAIN: Current domain for domain-specific configurations
 */

// Algolia index name from environment variables
// Used to specify which Algolia index to search against
export const indexName = process.env.NEXT_PUBLIC_ALGOLIA_INDEX as string

// Main Algolia search client for performing search operations
// Initialized with application ID and search-only API key for security
// This client is used for all search queries and autocomplete functionality
export const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)

// Algolia recommendation client for related content suggestions
// Uses the same credentials as search client but provides recommendation-specific functionality
// Used for "related articles", "trending content", and personalized recommendations
export const recommendClient = recommend(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)

// Current domain from environment variables
// Used for domain-specific search configurations and filtering
export const nextPublicDomain = process.env.NEXT_PUBLIC_DOMAIN as string

// List of domains that have Algolia search enabled
// Used to conditionally enable/disable search functionality based on current domain
// Currently only Altus Group main domain has full search capabilities
export const includedDomains = ['domainAltusGroupCom']
